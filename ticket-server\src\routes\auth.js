/**
 * 认证相关路由
 */
const express = require('express');
const router = express.Router();
const { generateToken, refreshToken } = require('../utils/jwt');
const { loginRules, registerRules } = require('../middlewares/validationRules');
const { validate } = require('../middlewares/validator');
const { AppError } = require('../middlewares/error');
const logger = require('../utils/logger');

// 登录路由
router.post('/login', loginRules, validate, async (req, res, next) => {
  try {
    const { username, password } = req.body;
    
    // 这里应该是验证用户名和密码的逻辑
    // 作为示例，我们只检查是否提供了用户名和密码
    if (!username || !password) {
      throw new AppError('用户名和密码不能为空', 400);
    }
    
    logger.info(`用户 ${username} 尝试登录`);
    
    // 模拟用户验证成功
    const user = { id: 1, username };
    
    // 生成JWT令牌
    const token = generateToken(user);
    
    // 返回令牌和用户信息
    res.success({
      token,
      user
    }, '登录成功');
  } catch (error) {
    logger.error(`登录失败: ${error.message}`);
    next(error);
  }
});

// 注册路由
router.post('/register', registerRules, validate, async (req, res, next) => {
  try {
    const { username, password, email } = req.body;
    
    // 这里应该是创建新用户的逻辑
    // 作为示例，我们只检查是否提供了必要的信息
    if (!username || !password || !email) {
      throw new AppError('用户名、密码和邮箱不能为空', 400);
    }
    
    logger.info(`新用户注册: ${username}, ${email}`);
    
    // 模拟用户创建成功
    const user = { id: 2, username, email };
    
    // 生成JWT令牌
    const token = generateToken(user);
    
    // 返回令牌和用户信息
    res.created({
      token,
      user
    }, '注册成功');
  } catch (error) {
    logger.error(`注册失败: ${error.message}`);
    next(error);
  }
});

// 刷新令牌路由
router.post('/refresh-token', async (req, res, next) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      throw new AppError('未提供令牌', 400);
    }
    
    logger.info('尝试刷新令牌');
    
    // 刷新令牌
    const newToken = refreshToken(token);
    
    res.success({
      token: newToken
    }, '令牌刷新成功');
  } catch (error) {
    logger.error(`刷新令牌失败: ${error.message}`);
    next(error);
  }
});

module.exports = router;