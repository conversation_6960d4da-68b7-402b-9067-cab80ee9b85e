/**
 * 请求验证中间件
 * 使用express-validator验证请求数据
 */
const { validationResult } = require('express-validator');
const logger = require('../utils/logger');

/**
 * 验证请求数据的中间件
 * 使用express-validator验证规则
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    logger.warn(`请求验证失败: ${req.originalUrl} - ${JSON.stringify(errorMessages)}`);
    return res.status(400).json({ success: false, message: '请求数据验证失败', errors: errorMessages });
  }
  next();
};

module.exports = {
  validate
};