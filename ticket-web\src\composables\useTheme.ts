import { ref, computed, watch } from 'vue'

/**
 * 主题类型
 */
export type Theme = 'light' | 'dark' | 'auto'

/**
 * 主题状态
 */
const theme = ref<Theme>('light')

/**
 * 主题管理 composable
 * 提供主题切换和状态管理功能
 */
export const useTheme = () => {
  /**
   * 获取系统主题偏好
   */
  const getSystemTheme = (): 'light' | 'dark' => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return 'light'
  }

  /**
   * 计算当前实际主题
   */
  const actualTheme = computed(() => {
    if (theme.value === 'auto') {
      return getSystemTheme()
    }
    return theme.value
  })

  /**
   * 是否为深色主题
   */
  const isDark = computed(() => actualTheme.value === 'dark')

  /**
   * 设置主题
   * @param newTheme 新主题
   */
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    applyTheme()
  }

  /**
   * 切换主题
   */
  const toggleTheme = () => {
    const currentTheme = actualTheme.value
    setTheme(currentTheme === 'light' ? 'dark' : 'light')
  }

  /**
   * 应用主题到DOM
   */
  const applyTheme = () => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement
      const currentTheme = actualTheme.value
      
      if (currentTheme === 'dark') {
        root.classList.add('dark')
      } else {
        root.classList.remove('dark')
      }
    }
  }

  /**
   * 初始化主题
   */
  const initTheme = () => {
    // 从localStorage读取保存的主题
    const savedTheme = localStorage.getItem('theme') as Theme
    if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
      theme.value = savedTheme
    }
    
    applyTheme()
    
    // 监听系统主题变化
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleChange = () => {
        if (theme.value === 'auto') {
          applyTheme()
        }
      }
      
      mediaQuery.addEventListener('change', handleChange)
      
      // 返回清理函数
      return () => {
        mediaQuery.removeEventListener('change', handleChange)
      }
    }
  }

  // 监听主题变化
  watch(theme, applyTheme, { immediate: true })

  return {
    theme: computed(() => theme.value),
    actualTheme,
    isDark,
    setTheme,
    toggleTheme,
    initTheme
  }
}

// 导出单例实例
export default useTheme