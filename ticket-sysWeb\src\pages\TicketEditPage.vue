<template>
  <div class="p-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-4">
        <button
          @click="goBack"
          class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
        >
          <ArrowLeft class="w-5 h-5" />
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            {{ isEdit ? '编辑票据' : '新建票据' }}
          </h1>
          <p v-if="isEdit && form.ticketNumber" class="text-sm text-gray-500">
            票据编号: {{ form.ticketNumber }}
          </p>
        </div>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="max-w-4xl mx-auto">
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 主要表单 -->
          <div class="lg:col-span-2 space-y-6">
            <!-- 基本信息 -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h2>
              
              <div class="space-y-4">
                <!-- 票据标题 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    票据标题 <span class="text-red-500">*</span>
                  </label>
                  <input
                    v-model="form.title"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入票据标题"
                    :class="{ 'border-red-500': errors.title }"
                  />
                  <p v-if="errors.title" class="mt-1 text-sm text-red-600">{{ errors.title }}</p>
                </div>

                <!-- 票据类型和金额 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      票据类型 <span class="text-red-500">*</span>
                    </label>
                    <select
                      v-model="form.type"
                      required
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      :class="{ 'border-red-500': errors.type }"
                    >
                      <option value="">请选择类型</option>
                      <option value="income">收入</option>
                      <option value="expense">支出</option>
                      <option value="transfer">转账</option>
                      <option value="refund">退款</option>
                    </select>
                    <p v-if="errors.type" class="mt-1 text-sm text-red-600">{{ errors.type }}</p>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      金额 <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                      <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                      <input
                        v-model.number="form.amount"
                        type="number"
                        step="0.01"
                        min="0"
                        required
                        class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="0.00"
                        :class="{ 'border-red-500': errors.amount }"
                      />
                    </div>
                    <p v-if="errors.amount" class="mt-1 text-sm text-red-600">{{ errors.amount }}</p>
                  </div>
                </div>

                <!-- 票据描述 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    票据描述
                  </label>
                  <textarea
                    v-model="form.description"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入票据描述（可选）"
                  ></textarea>
                </div>

                <!-- 到期时间 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    到期时间
                  </label>
                  <input
                    v-model="form.dueDate"
                    type="datetime-local"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            <!-- 附件上传 -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">附件</h2>
              
              <!-- 文件上传区域 -->
              <div
                @drop="handleDrop"
                @dragover.prevent
                @dragenter.prevent
                class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors"
                :class="{ 'border-blue-400 bg-blue-50': isDragging }"
              >
                <Upload class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-600 mb-2">拖拽文件到此处，或</p>
                <label class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors">
                  <input
                    ref="fileInput"
                    type="file"
                    multiple
                    class="hidden"
                    @change="handleFileSelect"
                  />
                  选择文件
                </label>
                <p class="text-sm text-gray-500 mt-2">支持多个文件，单个文件不超过 10MB</p>
              </div>

              <!-- 已选择的文件列表 -->
              <div v-if="selectedFiles.length > 0" class="mt-4">
                <h3 class="text-sm font-medium text-gray-700 mb-2">已选择的文件</h3>
                <div class="space-y-2">
                  <div
                    v-for="(file, index) in selectedFiles"
                    :key="index"
                    class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div class="flex items-center">
                      <Paperclip class="w-4 h-4 text-gray-400 mr-2" />
                      <div>
                        <p class="text-sm font-medium text-gray-900">{{ file.name }}</p>
                        <p class="text-sm text-gray-500">{{ formatFileSize(file.size) }}</p>
                      </div>
                    </div>
                    <button
                      @click="removeFile(index)"
                      class="text-red-600 hover:text-red-800"
                    >
                      <X class="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 侧边栏 -->
          <div class="space-y-6">
            <!-- 状态设置 -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">状态设置</h2>
              
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    票据状态
                  </label>
                  <select
                    v-model="form.status"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="pending">待处理</option>
                    <option value="processing">处理中</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                  </select>
                </div>

                <!-- 状态预览 -->
                <div class="p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">当前状态:</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusColor(form.status)">
                      {{ getStatusLabel(form.status) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">操作</h2>
              
              <div class="space-y-3">
                <button
                  type="submit"
                  :disabled="loading"
                  class="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <RefreshCw v-if="loading" class="w-4 h-4 mr-2 animate-spin" />
                  <Save v-else class="w-4 h-4 mr-2" />
                  {{ loading ? '保存中...' : (isEdit ? '更新票据' : '创建票据') }}
                </button>
                
                <button
                  type="button"
                  @click="handleSaveDraft"
                  :disabled="loading"
                  class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <FileText class="w-4 h-4 mr-2" />
                  保存草稿
                </button>
                
                <button
                  type="button"
                  @click="goBack"
                  class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <ArrowLeft class="w-4 h-4 mr-2" />
                  取消
                </button>
              </div>
            </div>

            <!-- 预览信息 -->
            <div v-if="form.title || form.amount" class="bg-white rounded-lg shadow-sm border p-6">
              <h2 class="text-lg font-semibold text-gray-900 mb-4">预览</h2>
              
              <div class="space-y-3">
                <div v-if="form.title">
                  <span class="text-sm text-gray-600">标题:</span>
                  <p class="text-sm font-medium text-gray-900">{{ form.title }}</p>
                </div>
                
                <div v-if="form.type && form.amount">
                  <span class="text-sm text-gray-600">金额:</span>
                  <p class="text-sm font-medium" :class="getAmountColor(form.type)">
                    {{ formatAmount(form.amount, form.type) }}
                  </p>
                </div>
                
                <div v-if="form.type">
                  <span class="text-sm text-gray-600">类型:</span>
                  <div class="flex items-center mt-1">
                    <component :is="getTypeIcon(form.type)" class="w-4 h-4 mr-2" :class="getTypeColor(form.type)" />
                    <span class="text-sm text-gray-900">{{ getTypeLabel(form.type) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeft,
  Save,
  FileText,
  Upload,
  Paperclip,
  X,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft,
  RotateCcw
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import dayjs from 'dayjs'
import { ticketApi } from '@/api'
import type { TicketForm } from '@/types'
import { TicketType, TicketStatus } from '@/types'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const isDragging = ref(false)
const selectedFiles = ref<File[]>([])
const fileInput = ref<HTMLInputElement>()

// 表单数据
const form = reactive<TicketForm>({
  title: '',
  type: '' as TicketType,
  amount: 0,
  description: '',
  status: TicketStatus.PENDING,
  dueDate: ''
})

// 表单验证错误
const errors = reactive({
  title: '',
  type: '',
  amount: ''
})

// 计算属性
const isEdit = computed(() => {
  return route.name === 'ticket-edit' && route.params.id
})

/**
 * 获取票据详情（编辑模式）
 */
const fetchTicket = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const ticketId = route.params.id as string
    
    const response = await ticketApi.getTicket(ticketId)
    const ticket = response
    
    // 填充表单数据
    Object.assign(form, {
      title: ticket.title,
      type: ticket.type,
      amount: ticket.amount,
      description: ticket.description || '',
      status: ticket.status,
      dueDate: ticket.dueDate ? dayjs(ticket.dueDate).format('YYYY-MM-DDTHH:mm') : '',
      ticketNumber: ticket.ticketNumber
    })
  } catch (error) {
    console.error('获取票据详情失败:', error)
    toast.error('获取票据详情失败')
    router.push('/tickets')
  } finally {
    loading.value = false
  }
}

/**
 * 表单验证
 */
const validateForm = () => {
  // 清空之前的错误
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
  
  let isValid = true
  
  // 验证标题
  if (!form.title.trim()) {
    errors.title = '请输入票据标题'
    isValid = false
  }
  
  // 验证类型
  if (!form.type) {
    errors.type = '请选择票据类型'
    isValid = false
  }
  
  // 验证金额
  if (!form.amount || form.amount <= 0) {
    errors.amount = '请输入有效的金额'
    isValid = false
  }
  
  return isValid
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  if (!validateForm()) {
    toast.error('请检查表单输入')
    return
  }
  
  try {
    loading.value = true
    
    const submitData = {
      ...form,
      dueDate: form.dueDate ? new Date(form.dueDate).toISOString() : undefined
    }
    
    if (isEdit.value) {
      // 更新票据
      const ticketId = route.params.id as string
      await ticketApi.updateTicket(ticketId, submitData)
      toast.success('票据更新成功')
    } else {
      // 创建票据
      await ticketApi.createTicket(submitData)
      toast.success('票据创建成功')
    }
    
    // 跳转到票据列表
    router.push('/tickets')
  } catch (error) {
    console.error('保存票据失败:', error)
    toast.error('保存失败')
  } finally {
    loading.value = false
  }
}

/**
 * 保存草稿
 */
const handleSaveDraft = () => {
  // 保存到本地存储
  const draftKey = isEdit.value ? `ticket-draft-${route.params.id}` : 'ticket-draft-new'
  localStorage.setItem(draftKey, JSON.stringify(form))
  toast.success('草稿已保存')
}

/**
 * 加载草稿
 */
const loadDraft = () => {
  const draftKey = isEdit.value ? `ticket-draft-${route.params.id}` : 'ticket-draft-new'
  const draft = localStorage.getItem(draftKey)
  
  if (draft && !isEdit.value) {
    try {
      const draftData = JSON.parse(draft)
      Object.assign(form, draftData)
      toast.success('已加载草稿')
    } catch (error) {
      console.error('加载草稿失败:', error)
    }
  }
}

/**
 * 处理文件选择
 */
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    addFiles(Array.from(target.files))
  }
}

/**
 * 处理文件拖拽
 */
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragging.value = false
  
  if (event.dataTransfer?.files) {
    addFiles(Array.from(event.dataTransfer.files))
  }
}

/**
 * 添加文件
 */
const addFiles = (files: File[]) => {
  const validFiles = files.filter(file => {
    // 检查文件大小（10MB限制）
    if (file.size > 10 * 1024 * 1024) {
      toast.error(`文件 ${file.name} 超过 10MB 限制`)
      return false
    }
    return true
  })
  
  selectedFiles.value.push(...validFiles)
  
  if (validFiles.length > 0) {
    toast.success(`已添加 ${validFiles.length} 个文件`)
  }
}

/**
 * 移除文件
 */
const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 获取类型图标
 */
const getTypeIcon = (type: TicketType) => {
  const icons = {
    income: TrendingUp,
    expense: TrendingDown,
    transfer: ArrowRightLeft,
    refund: RotateCcw
  }
  return icons[type] || TrendingUp
}

/**
 * 获取类型颜色
 */
const getTypeColor = (type: TicketType) => {
  const colors = {
    income: 'text-green-600',
    expense: 'text-red-600',
    transfer: 'text-blue-600',
    refund: 'text-orange-600'
  }
  return colors[type] || 'text-gray-600'
}

/**
 * 获取类型标签
 */
const getTypeLabel = (type: TicketType) => {
  const labels = {
    income: '收入',
    expense: '支出',
    transfer: '转账',
    refund: '退款'
  }
  return labels[type] || type
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status: TicketStatus) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  }
  return colors[status] || 'bg-gray-100 text-gray-800'
}

/**
 * 获取状态标签
 */
const getStatusLabel = (status: TicketStatus) => {
  const labels = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

/**
 * 获取金额颜色
 */
const getAmountColor = (type: TicketType) => {
  const colors = {
    income: 'text-green-600',
    expense: 'text-red-600',
    transfer: 'text-blue-600',
    refund: 'text-orange-600'
  }
  return colors[type] || 'text-gray-900'
}

/**
 * 格式化金额
 */
const formatAmount = (amount: number, type: TicketType) => {
  const prefix = type === TicketType.INCOME ? '+' : type === TicketType.EXPENSE ? '-' : ''
  return `${prefix}¥${amount.toLocaleString()}`
}

/**
 * 格式化文件大小
 */
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

// 组件挂载时的操作
onMounted(() => {
  if (isEdit.value) {
    fetchTicket()
  } else {
    loadDraft()
  }
})
</script>