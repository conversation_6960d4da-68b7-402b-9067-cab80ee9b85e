<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="max-w-md w-full space-y-8 p-8">
      <!-- Logo 和标题 -->
      <div class="text-center">
        <div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center">
          <FileText class="h-8 w-8 text-white" />
        </div>
        <h2 class="mt-6 text-3xl font-bold text-gray-900">票据助手管理平台</h2>
        <p class="mt-2 text-sm text-gray-600">请登录您的账户</p>
      </div>

      <!-- 登录表单 -->
      <form @submit.prevent="handleLogin" class="mt-8 space-y-6">
        <div class="space-y-4">
          <!-- 用户名输入框 -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">
              用户名
            </label>
            <div class="mt-1 relative">
              <input
                id="username"
                v-model="loginForm.username"
                type="text"
                required
                :class="[
                  'appearance-none relative block w-full px-3 py-3 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                  errors.username ? 'border-red-300' : ''
                ]"
                placeholder="请输入用户名"
              >
              <User class="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            </div>
            <p v-if="errors.username" class="mt-1 text-sm text-red-600">{{ errors.username }}</p>
          </div>

          <!-- 密码输入框 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              密码
            </label>
            <div class="mt-1 relative">
              <input
                id="password"
                v-model="loginForm.password"
                :type="showPassword ? 'text' : 'password'"
                required
                :class="[
                  'appearance-none relative block w-full px-3 py-3 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm',
                  errors.password ? 'border-red-300' : ''
                ]"
                placeholder="请输入密码"
              >
              <Lock class="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-3 h-5 w-5 text-gray-400 hover:text-gray-600"
              >
                <Eye v-if="!showPassword" class="h-5 w-5" />
                <EyeOff v-else class="h-5 w-5" />
              </button>
            </div>
            <p v-if="errors.password" class="mt-1 text-sm text-red-600">{{ errors.password }}</p>
          </div>
        </div>

        <!-- 记住我和忘记密码 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="rememberMe"
              type="checkbox"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <label for="remember-me" class="ml-2 block text-sm text-gray-700">
              记住我
            </label>
          </div>
          <div class="text-sm">
            <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
              忘记密码？
            </a>
          </div>
        </div>

        <!-- 登录按钮 -->
        <div>
          <button
            type="submit"
            :disabled="loading"
            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <Loader2 class="h-5 w-5 text-white animate-spin" />
            </span>
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </div>

        <!-- 注册链接 -->
        <div class="text-center">
          <span class="text-sm text-gray-600">还没有账户？</span>
          <router-link
            to="/register"
            class="ml-1 text-sm font-medium text-blue-600 hover:text-blue-500"
          >
            立即注册
          </router-link>
        </div>
      </form>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { FileText, User, Lock, Eye, EyeOff, Loader2 } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import { useUserStore } from '@/stores'
import { authApi } from '@/api'
import type { LoginForm } from '@/types'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const rememberMe = ref(false)

// 表单数据
const loginForm = reactive<LoginForm>({
  username: '',
  password: ''
})

// 表单验证错误
const errors = reactive({
  username: '',
  password: ''
})

/**
 * 验证表单数据
 * @returns {boolean} 验证是否通过
 */
const validateForm = (): boolean => {
  // 重置错误信息
  errors.username = ''
  errors.password = ''
  
  let isValid = true
  
  // 验证用户名
  if (!loginForm.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  } else if (loginForm.username.length < 3) {
    errors.username = '用户名至少3个字符'
    isValid = false
  }
  
  // 验证密码
  if (!loginForm.password) {
    errors.password = '请输入密码'
    isValid = false
  } else if (loginForm.password.length < 6) {
    errors.password = '密码至少6个字符'
    isValid = false
  }
  
  return isValid
}

/**
 * 处理登录提交
 */
const handleLogin = async () => {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    // 调用管理员登录 API
    const response = await authApi.login(loginForm)
    
    console.log('登录响应:', response)
    
    // 检查响应数据结构 - 处理可能的双层嵌套
    let responseData = response
    if (response && typeof response === 'object' && 'data' in response) {
      responseData = response.data
      // 处理可能存在的双层嵌套 data
      if (responseData && typeof responseData === 'object' && 'data' in responseData) {
        responseData = responseData.data
      }
    }
    
    console.log('处理后的响应数据:', responseData)
    
    // 兼容不同的响应数据结构
    if ((responseData && responseData.status === 'success' && responseData.admin && responseData.token) ||
        (responseData && responseData.admin && responseData.token)) {
      // 保存管理员信息和 token
      console.log('管理员信息:', responseData.admin)
      console.log('Token:', responseData.token)
      
      // 先设置token，再设置admin信息
      userStore.setToken(responseData.token)
      userStore.setAdmin(responseData.admin)
      
      // 如果选择记住我，保存到本地存储
      if (rememberMe.value) {
        localStorage.setItem('rememberMe', 'true')
        localStorage.setItem('savedUsername', loginForm.username)
      } else {
        localStorage.removeItem('rememberMe')
        localStorage.removeItem('savedUsername')
      }
      
      toast.success(responseData.message || '登录成功！')
      
      // 确保状态更新后再跳转
      await new Promise(resolve => setTimeout(resolve, 200))
      
      // 使用replace避免返回到登录页
      await router.replace('/')
    } else {
      toast.error(responseData?.message || response?.message || '登录失败')
    }
    
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 显示错误信息
    const errorMessage = error.response?.data?.message || error.message || '登录失败，请检查用户名和密码'
    toast.error(errorMessage)
    
    // 如果是用户名或密码错误，清空密码
    if (error.response?.status === 401 || error.response?.status === 400) {
      loginForm.password = ''
    }
  } finally {
    loading.value = false
  }
}

/**
 * 初始化表单数据
 */
const initForm = () => {
  // 如果之前选择了记住我，自动填充用户名
  const rememberMeFlag = localStorage.getItem('rememberMe')
  const savedUsername = localStorage.getItem('savedUsername')
  
  if (rememberMeFlag === 'true' && savedUsername) {
    loginForm.username = savedUsername
    rememberMe.value = true
  }
}

// 初始化
initForm()
</script>