-- 票据管理系统数据库建表语句
-- 基于票据录入页面分析生成
-- 创建时间：2024年12月

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS ticket_management 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE ticket_management;

-- 1. 用户表
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(36) PRIMARY KEY COMMENT '用户ID（UUID）',
    name VARCHAR(100) NOT NULL COMMENT '用户姓名',
    email VARCHAR(255) NOT NULL UNIQUE COMMENT '用户邮箱',
    role ENUM('user', 'enterprise') NOT NULL DEFAULT 'user' COMMENT '用户角色：普通用户/企业用户',
    avatar VARCHAR(500) COMMENT '用户头像URL',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 票据主表
CREATE TABLE IF NOT EXISTS tickets (
    id VARCHAR(36) PRIMARY KEY COMMENT '票据ID（UUID）',
    type ENUM('invoice', 'receipt', 'check', 'contract', 'other') NOT NULL DEFAULT 'invoice' COMMENT '票据类型：发票/收据/支票/合同/其他',
    title VARCHAR(200) NOT NULL COMMENT '票据标题',
    amount DECIMAL(15,2) NOT NULL DEFAULT 0.00 COMMENT '票据金额',
    date DATE NOT NULL COMMENT '票据日期',
    description TEXT COMMENT '票据描述',
    image_url VARCHAR(1000) COMMENT '票据图片URL',
    category VARCHAR(50) COMMENT '票据分类',
    status ENUM('pending', 'verified', 'rejected') NOT NULL DEFAULT 'pending' COMMENT '票据状态：待处理/已验证/已拒绝',
    created_by VARCHAR(36) NOT NULL COMMENT '创建者用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_date (date),
    INDEX idx_amount (amount),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at),
    INDEX idx_title (title),
    
    -- 复合索引
    INDEX idx_user_status (created_by, status),
    INDEX idx_date_amount (date, amount),
    INDEX idx_type_status (type, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据主表';

-- 3. 票据标签表
CREATE TABLE IF NOT EXISTS ticket_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
    ticket_id VARCHAR(36) NOT NULL COMMENT '票据ID',
    tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 外键约束
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
    
    -- 唯一约束（同一票据不能有重复标签）
    UNIQUE KEY uk_ticket_tag (ticket_id, tag_name),
    
    -- 索引
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_tag_name (tag_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据标签表';

-- 4. 票据附件表
CREATE TABLE IF NOT EXISTS ticket_attachments (
    id VARCHAR(36) PRIMARY KEY COMMENT '附件ID（UUID）',
    ticket_id VARCHAR(36) NOT NULL COMMENT '票据ID',
    name VARCHAR(255) NOT NULL COMMENT '附件名称',
    url VARCHAR(1000) NOT NULL COMMENT '附件URL',
    type VARCHAR(100) NOT NULL COMMENT '附件类型（MIME类型）',
    size BIGINT NOT NULL DEFAULT 0 COMMENT '附件大小（字节）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 外键约束
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据附件表';

-- 5. 票据验证结果表
CREATE TABLE IF NOT EXISTS ticket_verifications (
    id VARCHAR(36) PRIMARY KEY COMMENT '验证ID（UUID）',
    ticket_id VARCHAR(36) NOT NULL COMMENT '票据ID',
    is_valid BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否有效',
    confidence DECIMAL(5,2) COMMENT '置信度（0-100）',
    issues JSON COMMENT '问题列表（JSON格式）',
    verified_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '验证时间',
    verified_by VARCHAR(36) COMMENT '验证者用户ID',
    message TEXT COMMENT '验证消息',
    
    -- 验证详情字段
    tax_number_valid BOOLEAN COMMENT '税号是否有效',
    amount_valid BOOLEAN COMMENT '金额是否有效',
    date_valid BOOLEAN COMMENT '日期是否有效',
    issuer_valid BOOLEAN COMMENT '开票方是否有效',
    
    -- 外键约束
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (verified_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- 唯一约束（每个票据只能有一个验证结果）
    UNIQUE KEY uk_ticket_verification (ticket_id),
    
    -- 索引
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_is_valid (is_valid),
    INDEX idx_verified_by (verified_by),
    INDEX idx_verified_at (verified_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据验证结果表';

-- 6. 票据分类字典表
CREATE TABLE IF NOT EXISTS ticket_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '分类名称',
    description VARCHAR(200) COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_name (name),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据分类字典表';

-- 7. 票据操作日志表
CREATE TABLE IF NOT EXISTS ticket_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    ticket_id VARCHAR(36) NOT NULL COMMENT '票据ID',
    user_id VARCHAR(36) NOT NULL COMMENT '操作用户ID',
    action ENUM('create', 'update', 'delete', 'verify', 'reject') NOT NULL COMMENT '操作类型',
    old_data JSON COMMENT '操作前数据（JSON格式）',
    new_data JSON COMMENT '操作后数据（JSON格式）',
    remark VARCHAR(500) COMMENT '操作备注',
    ip_address VARCHAR(45) COMMENT '操作IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    -- 外键约束
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_ticket_id (ticket_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    
    -- 复合索引
    INDEX idx_ticket_action (ticket_id, action),
    INDEX idx_user_action (user_id, action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据操作日志表';

-- 插入默认分类数据
INSERT INTO ticket_categories (name, description, sort_order) VALUES
('办公用品', '办公相关用品采购', 1),
('餐饮', '餐饮消费相关', 2),
('交通', '交通出行相关', 3),
('住宿', '住宿相关费用', 4),
('通讯', '通讯费用相关', 5),
('水电费', '水电燃气等公用事业费', 6),
('租金', '房屋租赁相关', 7),
('维修', '设备维修保养', 8),
('培训', '培训教育相关', 9),
('其他', '其他未分类项目', 10)
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- 创建视图：票据统计视图
CREATE OR REPLACE VIEW v_ticket_stats AS
SELECT 
    COUNT(*) as total,
    COUNT(CASE WHEN status = 'verified' THEN 1 END) as verified,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
    COALESCE(SUM(amount), 0) as total_amount,
    COALESCE(AVG(amount), 0) as average_amount,
    created_by
FROM tickets 
GROUP BY created_by;

-- 创建视图：票据详情视图
CREATE OR REPLACE VIEW v_ticket_details AS
SELECT 
    t.id,
    t.type,
    t.title,
    t.amount,
    t.date,
    t.description,
    t.image_url,
    t.category,
    t.status,
    t.created_by,
    t.created_at,
    t.updated_at,
    u.name as creator_name,
    u.email as creator_email,
    GROUP_CONCAT(tt.tag_name) as tags,
    tv.is_valid,
    tv.confidence,
    tv.verified_at,
    tv.verified_by
FROM tickets t
LEFT JOIN users u ON t.created_by = u.id
LEFT JOIN ticket_tags tt ON t.id = tt.ticket_id
LEFT JOIN ticket_verifications tv ON t.id = tv.ticket_id
GROUP BY t.id;

-- 添加注释说明
/*
数据库设计说明：

1. 主要特性：
   - 使用UTF8MB4字符集，支持emoji和特殊字符
   - 使用UUID作为主键，避免ID冲突
   - 合理的索引设计，提高查询性能
   - 外键约束保证数据完整性
   - JSON字段存储复杂数据结构

2. 表结构说明：
   - users: 用户基础信息
   - tickets: 票据主表，存储核心信息
   - ticket_tags: 票据标签，支持多标签
   - ticket_attachments: 票据附件管理
   - ticket_verifications: 票据验证结果
   - ticket_categories: 分类字典
   - ticket_logs: 操作日志审计

3. 性能优化：
   - 合理的索引设计
   - 分表存储（标签、附件等）
   - 视图简化查询
   - 适当的数据类型选择

4. 扩展性：
   - 支持多种票据类型
   - 灵活的标签系统
   - 完整的审计日志
   - 可扩展的验证机制
*/