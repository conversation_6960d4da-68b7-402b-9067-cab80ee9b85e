/**
 * 用户模型
 * 处理普通用户相关的数据库操作
 */
const mysql = require('mysql2/promise');
const { createPool } = require('../config/db');
const logger = require('../utils/logger');
const { generateSalt, hashPassword, verifyPassword } = require('../utils/crypto');

/**
 * 初始化用户表
 * 检查用户表是否存在，如果不存在则创建
 * @returns {Promise<void>}
 */
const initUserTable = async () => {
  try {
    const pool = await createPool();
    const connection = await pool.getConnection();

    // 检查表是否存在
    const [tables] = await connection.query(
      "SHOW TABLES LIKE 'users'"
    );

    // 如果表不存在，创建表
    if (tables.length === 0) {
      logger.info('创建用户表');
      await connection.query(`
        CREATE TABLE users (
          user_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
          username varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名，用于登录和显示',
          email varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '电子邮箱，用于登录和通知',
          mobile varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码，用于登录和验证',
          password_hash varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '加密后的密码',
          salt varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码加密盐值',
          avatar_url varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
          nickname varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户昵称',
          bio varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人简介',
          gender tinyint(4) DEFAULT '0' COMMENT '性别：0-未知 1-男 2-女',
          birthday date DEFAULT NULL COMMENT '生日',
          status tinyint(4) NOT NULL DEFAULT '1' COMMENT '账号状态：0-禁用 1-正常 2-未激活',
          last_login_time datetime DEFAULT NULL COMMENT '最后登录时间',
          last_login_ip varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
          register_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
          register_ip varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册IP',
          update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          is_verified tinyint(4) DEFAULT '0' COMMENT '是否认证：0-未认证 1-已认证',
          verification_code varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱/手机验证码',
          verification_expire datetime DEFAULT NULL COMMENT '验证码过期时间',
          role tinyint(4) DEFAULT '1' COMMENT '用户角色：1-普通用户 2-内容创作者 3-管理员',
          PRIMARY KEY (user_id),
          UNIQUE KEY idx_username (username),
          UNIQUE KEY idx_email (email),
          KEY idx_mobile (mobile),
          KEY idx_status (status),
          KEY idx_register_time (register_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'
      `);
      
      logger.info('用户表创建成功');
    }

    connection.release();
  } catch (error) {
    logger.error(`初始化用户表失败: ${error.message}`);
    throw error;
  }
};

/**
 * 获取所有用户列表（支持分页和筛选）
 * @param {Object} options - 查询选项
 * @param {number} options.page - 页码，默认1
 * @param {number} options.limit - 每页数量，默认10
 * @param {string} options.search - 搜索关键词
 * @param {number} options.status - 状态筛选
 * @returns {Promise<Object>} 包含用户列表和分页信息
 */
const getAllUsers = async (options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = null
    } = options;

    const pool = await createPool();
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];

    if (search) {
      whereConditions.push('(username LIKE ? OR email LIKE ? OR nickname LIKE ? OR real_name LIKE ?)');
      queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (status !== null) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 查询用户列表
    const [users] = await pool.query(`
      SELECT 
        user_id,
        username,
        email,
        mobile,
        avatar_url,
        nickname,
        bio,
        gender,
        birthday,
        status,
        last_login_time,
        last_login_ip,
        register_time,
        is_verified,
        role
      FROM users 
      ${whereClause}
      ORDER BY register_time DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]);

    // 查询总数
    const [countResult] = await pool.query(`
      SELECT COUNT(*) as total 
      FROM users 
      ${whereClause}
    `, queryParams);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    logger.error(`获取用户列表失败: ${error.message}`);
    throw error;
  }
};

/**
 * 根据用户ID获取用户信息
 * @param {number} userId - 用户ID
 * @returns {Promise<Object|null>} 用户信息或null
 */
const getUserById = async (userId) => {
  try {
    const pool = await createPool();
    const [rows] = await pool.query(`
      SELECT 
        user_id,
        username,
        email,
        mobile,
        avatar_url,
        nickname,
        bio,
        gender,
        birthday,
        status,
        last_login_time,
        last_login_ip,
        register_time,
        is_verified,
        role
      FROM users 
      WHERE user_id = ?
    `, [userId]);
    
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    logger.error(`获取用户信息失败: ${error.message}`);
    throw error;
  }
};

/**
 * 根据用户名查找用户
 * @param {string} username - 用户名
 * @returns {Promise<Object|null>} 用户信息或null
 */
const findUserByUsername = async (username) => {
  try {
    const pool = await createPool();
    const [rows] = await pool.query(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );
    
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    logger.error(`查找用户失败: ${error.message}`);
    throw error;
  }
};

/**
 * 根据邮箱查找用户
 * @param {string} email - 邮箱
 * @returns {Promise<Object|null>} 用户信息或null
 */
const findUserByEmail = async (email) => {
  try {
    const pool = await createPool();
    const [rows] = await pool.query(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );
    
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    logger.error(`查找用户失败: ${error.message}`);
    throw error;
  }
};

/**
 * 创建新用户
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} 创建的用户信息
 */
const createUser = async (userData) => {
  try {
    const {
      username,
      email,
      mobile,
      password,
      nickname,
      registerIp
    } = userData;

    // 生成盐值和密码哈希
    const salt = generateSalt();
    const passwordHash = hashPassword(password, salt);

    const pool = await createPool();
    const [result] = await pool.query(`
      INSERT INTO users (
        username, email, mobile, password_hash, salt, nickname, register_ip
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [username, email, mobile, passwordHash, salt, nickname, registerIp]);

    // 返回创建的用户信息
    return await getUserById(result.insertId);
  } catch (error) {
    logger.error(`创建用户失败: ${error.message}`);
    throw error;
  }
};

/**
 * 更新用户状态
 * @param {number} userId - 用户ID
 * @param {number} status - 新状态
 * @returns {Promise<void>}
 */
const updateUserStatus = async (userId, status) => {
  try {
    const pool = await createPool();
    await pool.query(
      'UPDATE users SET status = ?, update_time = NOW() WHERE user_id = ?',
      [status, userId]
    );
  } catch (error) {
    logger.error(`更新用户状态失败: ${error.message}`);
    throw error;
  }
};

/**
 * 更新用户最后登录信息
 * @param {number} userId - 用户ID
 * @param {string} ip - 登录IP
 * @returns {Promise<void>}
 */
const updateUserLastLogin = async (userId, ip) => {
  try {
    const pool = await createPool();
    await pool.query(
      'UPDATE users SET last_login_time = NOW(), last_login_ip = ? WHERE user_id = ?',
      [ip, userId]
    );
  } catch (error) {
    logger.error(`更新用户登录信息失败: ${error.message}`);
    throw error;
  }
};

/**
 * 删除用户（软删除，更新状态为禁用）
 * @param {number} userId - 用户ID
 * @returns {Promise<void>}
 */
const deleteUser = async (userId) => {
  try {
    const pool = await createPool();
    await pool.query(
      'UPDATE users SET status = 0, update_time = NOW() WHERE user_id = ?',
      [userId]
    );
  } catch (error) {
    logger.error(`删除用户失败: ${error.message}`);
    throw error;
  }
};

module.exports = {
  initUserTable,
  getAllUsers,
  getUserById,
  findUserByUsername,
  findUserByEmail,
  createUser,
  updateUserStatus,
  updateUserLastLogin,
  deleteUser,
  verifyPassword
};
