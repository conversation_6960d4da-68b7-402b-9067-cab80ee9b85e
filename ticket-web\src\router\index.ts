import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/useAuthStore'
import HomePage from '@/pages/HomePage.vue'
import CreateTicketPage from '@/pages/CreateTicketPage.vue'
import TicketListPage from '@/pages/TicketListPage.vue'
import TicketDetailPage from '@/pages/TicketDetailPage.vue'
import VerificationPage from '@/pages/VerificationPage.vue'
import StatisticsPage from '@/pages/StatisticsPage.vue'
import SettingsPage from '@/pages/SettingsPage.vue'
import UserManagementPage from '@/pages/UserManagementPage.vue'
import LoginPage from '@/pages/LoginPage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginPage,
      meta: { hideLayout: true }
    },
    {
      path: '/',
      name: 'home',
      component: HomePage,
      meta: { requiresAuth: true }
    },
    {
      path: '/create',
      name: 'create-ticket',
      component: CreateTicketPage,
      meta: { requiresAuth: true }
    },
    {
      path: '/tickets',
      name: 'ticket-list',
      component: TicketListPage,
      meta: { requiresAuth: true }
    },
    {
      path: '/tickets/:id',
      name: 'ticket-detail',
      component: TicketDetailPage,
      props: true,
      meta: { requiresAuth: true }
    },
    {
      path: '/verify',
      name: 'verification',
      component: VerificationPage,
      meta: { requiresAuth: true }
    },
    {
      path: '/analytics',
      name: 'statistics',
      component: StatisticsPage,
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'settings',
      component: SettingsPage,
      meta: { requiresAuth: true }
    },
    {
      path: '/users',
      name: 'user-management',
      component: UserManagementPage,
      meta: { requiresAuth: true, requiresEnterprise: true }
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 初始化认证状态（仅在首次访问时）
  if (!authStore.isAuthenticated && !authStore.user) {
    await authStore.initAuth()
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 需要认证但未登录，跳转到登录页
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else if (to.name === 'login' && authStore.isAuthenticated) {
    // 已登录用户访问登录页，跳转到首页
    next({ name: 'home' })
  } else if (to.meta.requiresEnterprise && authStore.user?.role !== 'enterprise') {
    // 需要企业用户权限但当前用户不是企业用户
    next({ name: 'home' })
  } else {
    // 正常访问
    next()
  }
})

export default router
