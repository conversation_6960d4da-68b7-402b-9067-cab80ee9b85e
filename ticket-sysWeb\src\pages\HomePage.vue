<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">欢迎回来，{{ admin?.real_name || admin?.username || user?.username }}</h1>
        <p class="text-gray-600">这里是您的票据管理概览</p>
      </div>
      <div class="text-sm text-gray-500">
        {{ currentTime }}
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatCard
        title="总票据数"
        :value="statistics?.totalTickets || 0"
        icon="FileText"
        color="blue"
        :trend="{ value: 12, isPositive: true }"
      />
      <StatCard
        title="本月新增"
        :value="statistics?.monthlyNew || 0"
        icon="Plus"
        color="green"
        :trend="{ value: 8, isPositive: true }"
      />
      <StatCard
        title="待处理"
        :value="statistics?.pendingCount || 0"
        icon="Clock"
        color="yellow"
        :trend="{ value: 3, isPositive: false }"
      />
      <StatCard
        title="总金额"
        :value="formatCurrency(statistics?.totalAmount || 0)"
        icon="DollarSign"
        color="purple"
        :trend="{ value: 15, isPositive: true }"
      />
    </div>

    <!-- 快速操作和图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 快速操作 -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h2>
          <div class="space-y-3">
            <button
              @click="navigateTo('/tickets/create')"
              class="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              <Plus class="w-5 h-5 mr-2" />
              新建票据
            </button>
            <button
              @click="navigateTo('/tickets')"
              class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
            >
              <Search class="w-5 h-5 mr-2" />
              搜索票据
            </button>
            <button
              @click="handleBatchImport"
              class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
            >
              <Upload class="w-5 h-5 mr-2" />
              批量导入
            </button>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">票据趋势</h2>
            <select
              v-model="chartPeriod"
              class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="week">最近一周</option>
              <option value="month">最近一月</option>
              <option value="quarter">最近三月</option>
            </select>
          </div>
          <div class="h-64">
            <TicketChart :period="chartPeriod" />
          </div>
        </div>
      </div>
    </div>

    <!-- 最近票据列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">最近票据</h2>
          <router-link
            to="/tickets"
            class="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            查看全部
          </router-link>
        </div>
      </div>
      <div class="p-6">
        <RecentTicketList :tickets="recentTickets" :loading="loadingTickets" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Plus, Search, Upload, FileText, Clock, DollarSign } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import { useUserStore, useStatisticsStore, useTicketStore } from '@/stores'
import { statisticsApi, ticketApi } from '@/api'
import StatCard from '@/components/StatCard.vue'
import TicketChart from '@/components/TicketChart.vue'
import RecentTicketList from '@/components/RecentTicketList.vue'
import type { Ticket } from '@/types'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()
const statisticsStore = useStatisticsStore()
const ticketStore = useTicketStore()

// 响应式数据
const chartPeriod = ref<'week' | 'month' | 'quarter'>('month')
const recentTickets = ref<Ticket[]>([])
const loadingTickets = ref(false)

// 计算属性
const user = computed(() => userStore.user)
const admin = computed(() => userStore.admin)
const statistics = computed(() => statisticsStore.statistics)

// 当前时间
const currentTime = computed(() => {
  return new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

/**
 * 格式化货币
 * @param amount 金额
 * @returns 格式化后的货币字符串
 */
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

/**
 * 导航到指定路由
 * @param path 路由路径
 */
const navigateTo = (path: string) => {
  router.push(path)
}

/**
 * 处理批量导入
 */
const handleBatchImport = () => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls,.csv'
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      // 这里可以添加文件上传和处理逻辑
      toast.success('文件上传功能开发中...')
    }
  }
  input.click()
}

/**
 * 加载统计数据
 */
const loadStatistics = async () => {
  try {
    const stats = await statisticsApi.getOverallStats()
    statisticsStore.setStatistics(stats)
  } catch (error) {
    console.error('加载统计数据失败:', error)
    toast.error('加载统计数据失败')
  }
}

/**
 * 加载最近票据
 */
const loadRecentTickets = async () => {
  loadingTickets.value = true
  try {
    const response = await ticketApi.getTickets({
      current: 1,
      pageSize: 5
    })
    recentTickets.value = response.list
  } catch (error) {
    console.error('加载最近票据失败:', error)
    toast.error('加载最近票据失败')
  } finally {
    loadingTickets.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  loadStatistics()
  loadRecentTickets()
})
</script>
