/**
 * 文件上传中间件
 * 使用Multer处理文件上传
 */
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { AppError } = require('./error');
const logger = require('../utils/logger');

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new AppError('不支持的文件类型', 400), false);
  }
};

// 创建Multer实例
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 限制文件大小为5MB
  }
});

/**
 * 处理Multer错误的中间件
 */
const handleMulterErrors = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    let message = '文件上传错误';
    let statusCode = 400;
    
    // 处理不同类型的Multer错误
    switch (err.code) {
      case 'LIMIT_FILE_SIZE':
        message = '文件大小超过限制';
        break;
      case 'LIMIT_FILE_COUNT':
        message = '上传的文件数量超过限制';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = '意外的文件字段';
        break;
      default:
        message = err.message;
    }
    
    logger.error(`文件上传错误: ${message}`);
    return res.status(statusCode).json({ success: false, message });
  }
  next(err);
};

module.exports = {
  upload,
  handleMulterErrors
};