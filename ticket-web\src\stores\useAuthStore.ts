import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { User, LoginRequest } from '@/types'
import { login as apiLogin, logout as apiLogout, getCurrentUser } from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isEnterprise = computed(() => user.value?.role === 'enterprise')

  /**
   * 初始化认证状态（从localStorage恢复）
   */
  const initAuth = async () => {
    const savedToken = localStorage.getItem('auth_token')
    const savedUser = localStorage.getItem('auth_user')
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        
        // 验证token是否仍然有效
        await getCurrentUser()
      } catch (err) {
        // token无效，清除本地存储
        clearAuth()
      }
    }
  }

  /**
   * 用户登录
   * @param loginData 登录数据
   */
  const login = async (loginData: LoginRequest) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await apiLogin(loginData)
      
      if (response.code === 200 && response.data.data) {
        const { user: userData, token: userToken } = response.data.data
        
        // 保存到状态
        user.value = userData
        token.value = userToken
        
        // 保存到localStorage
        localStorage.setItem('auth_token', userToken)
        localStorage.setItem('auth_user', JSON.stringify(userData))
        
        return { success: true }
      } else {
        error.value = response.message || '登录失败'
        return { success: false, message: error.value }
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 用户登出
   */
  const logout = async () => {
    isLoading.value = true
    
    try {
      await apiLogout()
    } catch (err) {
      console.warn('登出API调用失败:', err)
    } finally {
      clearAuth()
      isLoading.value = false
    }
  }

  /**
   * 清除认证状态
   */
  const clearAuth = () => {
    user.value = null
    token.value = null
    error.value = null
    localStorage.removeItem('auth_token')
    localStorage.removeItem('auth_user')
  }

  /**
   * 清除错误信息
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    error,
    
    // 计算属性
    isAuthenticated,
    isEnterprise,
    
    // 方法
    initAuth,
    login,
    logout,
    clearAuth,
    clearError
  }
})
