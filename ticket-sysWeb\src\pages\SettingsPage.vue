<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">系统设置</h1>
      <p class="text-gray-600 mt-1">管理系统配置和偏好设置</p>
    </div>

    <div class="max-w-4xl mx-auto">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- 侧边导航 -->
        <div class="lg:col-span-1">
          <nav class="bg-white rounded-lg shadow-sm border p-4">
            <ul class="space-y-2">
              <li v-for="tab in tabs" :key="tab.id">
                <button
                  @click="activeTab = tab.id"
                  class="w-full flex items-center px-3 py-2 text-left rounded-lg transition-colors"
                  :class="{
                    'bg-blue-100 text-blue-700': activeTab === tab.id,
                    'text-gray-700 hover:bg-gray-100': activeTab !== tab.id
                  }"
                >
                  <component :is="tab.icon" class="w-4 h-4 mr-3" />
                  {{ tab.name }}
                </button>
              </li>
            </ul>
          </nav>
        </div>

        <!-- 主要内容 -->
        <div class="lg:col-span-3">
          <!-- 基本设置 -->
          <div v-if="activeTab === 'basic'" class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">基本设置</h2>
            
            <form @submit.prevent="saveBasicSettings" class="space-y-6">
              <!-- 系统名称 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  系统名称
                </label>
                <input
                  v-model="basicSettings.systemName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入系统名称"
                />
              </div>
              
              <!-- 系统描述 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  系统描述
                </label>
                <textarea
                  v-model="basicSettings.systemDescription"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入系统描述"
                ></textarea>
              </div>
              
              <!-- 时区设置 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  时区
                </label>
                <select
                  v-model="basicSettings.timezone"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</option>
                  <option value="UTC">UTC (UTC+0)</option>
                  <option value="America/New_York">America/New_York (UTC-5)</option>
                </select>
              </div>
              
              <!-- 语言设置 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  默认语言
                </label>
                <select
                  v-model="basicSettings.language"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="zh-CN">简体中文</option>
                  <option value="en-US">English</option>
                </select>
              </div>
              
              <div class="flex justify-end">
                <button
                  type="submit"
                  :disabled="saving"
                  class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {{ saving ? '保存中...' : '保存设置' }}
                </button>
              </div>
            </form>
          </div>

          <!-- 安全设置 -->
          <div v-if="activeTab === 'security'" class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">安全设置</h2>
            
            <form @submit.prevent="saveSecuritySettings" class="space-y-6">
              <!-- 密码策略 -->
              <div>
                <h3 class="text-md font-medium text-gray-900 mb-3">密码策略</h3>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      最小密码长度
                    </label>
                    <input
                      v-model.number="securitySettings.minPasswordLength"
                      type="number"
                      min="6"
                      max="32"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div class="space-y-2">
                    <label class="flex items-center">
                      <input
                        v-model="securitySettings.requireUppercase"
                        type="checkbox"
                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span class="ml-2 text-sm text-gray-700">要求包含大写字母</span>
                    </label>
                    
                    <label class="flex items-center">
                      <input
                        v-model="securitySettings.requireNumbers"
                        type="checkbox"
                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span class="ml-2 text-sm text-gray-700">要求包含数字</span>
                    </label>
                    
                    <label class="flex items-center">
                      <input
                        v-model="securitySettings.requireSpecialChars"
                        type="checkbox"
                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span class="ml-2 text-sm text-gray-700">要求包含特殊字符</span>
                    </label>
                  </div>
                </div>
              </div>
              
              <!-- 会话设置 -->
              <div>
                <h3 class="text-md font-medium text-gray-900 mb-3">会话设置</h3>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      会话超时时间（分钟）
                    </label>
                    <input
                      v-model.number="securitySettings.sessionTimeout"
                      type="number"
                      min="5"
                      max="1440"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <label class="flex items-center">
                    <input
                      v-model="securitySettings.enableTwoFactor"
                      type="checkbox"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">启用双因素认证</span>
                  </label>
                </div>
              </div>
              
              <div class="flex justify-end">
                <button
                  type="submit"
                  :disabled="saving"
                  class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {{ saving ? '保存中...' : '保存设置' }}
                </button>
              </div>
            </form>
          </div>

          <!-- 通知设置 -->
          <div v-if="activeTab === 'notification'" class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">通知设置</h2>
            
            <form @submit.prevent="saveNotificationSettings" class="space-y-6">
              <!-- 邮件通知 -->
              <div>
                <h3 class="text-md font-medium text-gray-900 mb-3">邮件通知</h3>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      SMTP 服务器
                    </label>
                    <input
                      v-model="notificationSettings.smtpHost"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="smtp.example.com"
                    />
                  </div>
                  
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        端口
                      </label>
                      <input
                        v-model.number="notificationSettings.smtpPort"
                        type="number"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="587"
                      />
                    </div>
                    
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        加密方式
                      </label>
                      <select
                        v-model="notificationSettings.smtpEncryption"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="none">无</option>
                        <option value="tls">TLS</option>
                        <option value="ssl">SSL</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      发件人邮箱
                    </label>
                    <input
                      v-model="notificationSettings.fromEmail"
                      type="email"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>
              
              <!-- 通知类型 -->
              <div>
                <h3 class="text-md font-medium text-gray-900 mb-3">通知类型</h3>
                <div class="space-y-2">
                  <label class="flex items-center">
                    <input
                      v-model="notificationSettings.enableTicketNotification"
                      type="checkbox"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">票据状态变更通知</span>
                  </label>
                  
                  <label class="flex items-center">
                    <input
                      v-model="notificationSettings.enableUserNotification"
                      type="checkbox"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">用户操作通知</span>
                  </label>
                  
                  <label class="flex items-center">
                    <input
                      v-model="notificationSettings.enableSystemNotification"
                      type="checkbox"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span class="ml-2 text-sm text-gray-700">系统维护通知</span>
                  </label>
                </div>
              </div>
              
              <div class="flex justify-end space-x-3">
                <button
                  type="button"
                  @click="testEmailSettings"
                  class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  测试邮件
                </button>
                <button
                  type="submit"
                  :disabled="saving"
                  class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {{ saving ? '保存中...' : '保存设置' }}
                </button>
              </div>
            </form>
          </div>

          <!-- 系统信息 -->
          <div v-if="activeTab === 'system'" class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">系统信息</h2>
            
            <div class="space-y-6">
              <!-- 版本信息 -->
              <div>
                <h3 class="text-md font-medium text-gray-900 mb-3">版本信息</h3>
                <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">系统版本:</span>
                    <span class="text-sm font-medium text-gray-900">{{ systemInfo.version }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">构建时间:</span>
                    <span class="text-sm font-medium text-gray-900">{{ systemInfo.buildTime }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">运行时间:</span>
                    <span class="text-sm font-medium text-gray-900">{{ systemInfo.uptime }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 数据库信息 -->
              <div>
                <h3 class="text-md font-medium text-gray-900 mb-3">数据库信息</h3>
                <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">数据库类型:</span>
                    <span class="text-sm font-medium text-gray-900">{{ systemInfo.database.type }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">数据库版本:</span>
                    <span class="text-sm font-medium text-gray-900">{{ systemInfo.database.version }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600">连接状态:</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="systemInfo.database.connected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                      {{ systemInfo.database.connected ? '已连接' : '未连接' }}
                    </span>
                  </div>
                </div>
              </div>
              
              <!-- 系统统计 -->
              <div>
                <h3 class="text-md font-medium text-gray-900 mb-3">系统统计</h3>
                <div class="grid grid-cols-2 gap-4">
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-2xl font-bold text-blue-600">{{ systemInfo.stats.totalUsers }}</div>
                    <div class="text-sm text-gray-600">总用户数</div>
                  </div>
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-2xl font-bold text-green-600">{{ systemInfo.stats.totalTickets }}</div>
                    <div class="text-sm text-gray-600">总票据数</div>
                  </div>
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-2xl font-bold text-orange-600">{{ systemInfo.stats.activeUsers }}</div>
                    <div class="text-sm text-gray-600">活跃用户</div>
                  </div>
                  <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-2xl font-bold text-purple-600">{{ systemInfo.stats.todayTickets }}</div>
                    <div class="text-sm text-gray-600">今日票据</div>
                  </div>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="flex space-x-3">
                <button
                  @click="exportSystemData"
                  class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  导出系统数据
                </button>
                <button
                  @click="clearSystemCache"
                  class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  清理系统缓存
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  Settings,
  Shield,
  Bell,
  Info
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import { settingsApi } from '@/api'

// 响应式数据
const saving = ref(false)
const activeTab = ref('basic')

// 标签页配置
const tabs = [
  { id: 'basic', name: '基本设置', icon: Settings },
  { id: 'security', name: '安全设置', icon: Shield },
  { id: 'notification', name: '通知设置', icon: Bell },
  { id: 'system', name: '系统信息', icon: Info }
]

// 基本设置
const basicSettings = reactive({
  systemName: '票据助手管理平台',
  systemDescription: '专业的票据管理解决方案',
  timezone: 'Asia/Shanghai',
  language: 'zh-CN'
})

// 安全设置
const securitySettings = reactive({
  minPasswordLength: 8,
  requireUppercase: true,
  requireNumbers: true,
  requireSpecialChars: false,
  sessionTimeout: 30,
  enableTwoFactor: false
})

// 通知设置
const notificationSettings = reactive({
  smtpHost: '',
  smtpPort: 587,
  smtpEncryption: 'tls',
  fromEmail: '',
  enableTicketNotification: true,
  enableUserNotification: true,
  enableSystemNotification: false
})

// 系统信息
const systemInfo = reactive({
  version: '1.0.0',
  buildTime: '2024-01-15 10:30:00',
  uptime: '7天 12小时 30分钟',
  database: {
    type: 'MySQL',
    version: '8.0.32',
    connected: true
  },
  stats: {
    totalUsers: 156,
    totalTickets: 2847,
    activeUsers: 23,
    todayTickets: 45
  }
})

/**
 * 保存基本设置
 */
const saveBasicSettings = async () => {
  try {
    saving.value = true
    await settingsApi.updateSettings({ type: 'basic', ...basicSettings })
    toast.success('基本设置保存成功')
  } catch (error) {
    console.error('保存基本设置失败:', error)
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 保存安全设置
 */
const saveSecuritySettings = async () => {
  try {
    saving.value = true
    await settingsApi.updateSettings({ type: 'security', ...securitySettings })
    toast.success('安全设置保存成功')
  } catch (error) {
    console.error('保存安全设置失败:', error)
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 保存通知设置
 */
const saveNotificationSettings = async () => {
  try {
    saving.value = true
    await settingsApi.updateSettings({ type: 'notification', ...notificationSettings })
    toast.success('通知设置保存成功')
  } catch (error) {
    console.error('保存通知设置失败:', error)
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 测试邮件设置
 */
const testEmailSettings = async () => {
  try {
    await settingsApi.testEmailSettings(notificationSettings)
    toast.success('测试邮件发送成功')
  } catch (error) {
    console.error('测试邮件失败:', error)
    toast.error('测试邮件发送失败')
  }
}

/**
 * 导出系统数据
 */
const exportSystemData = () => {
  // 模拟导出功能
  const data = {
    exportTime: new Date().toISOString(),
    systemInfo,
    settings: {
      basic: basicSettings,
      security: securitySettings,
      notification: notificationSettings
    }
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `system-data-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  toast.success('系统数据导出成功')
}

/**
 * 清理系统缓存
 */
const clearSystemCache = async () => {
  if (!confirm('确定要清理系统缓存吗？这可能会影响系统性能。')) {
    return
  }
  
  try {
    await settingsApi.clearCache()
    toast.success('系统缓存清理成功')
  } catch (error) {
    console.error('清理缓存失败:', error)
    toast.error('清理缓存失败')
  }
}

/**
 * 获取系统设置
 */
const fetchSettings = async () => {
  try {
    const response = await settingsApi.getSettings()
    const settings = response.data
    
    // 更新设置数据
    if (settings.basic) {
      Object.assign(basicSettings, settings.basic)
    }
    if (settings.security) {
      Object.assign(securitySettings, settings.security)
    }
    if (settings.notification) {
      Object.assign(notificationSettings, settings.notification)
    }
  } catch (error) {
    console.error('获取系统设置失败:', error)
  }
}

/**
 * 获取系统信息
 */
const fetchSystemInfo = async () => {
  try {
    const response = await settingsApi.getSystemInfo()
    Object.assign(systemInfo, response.data)
  } catch (error) {
    console.error('获取系统信息失败:', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSettings()
  fetchSystemInfo()
})
</script>