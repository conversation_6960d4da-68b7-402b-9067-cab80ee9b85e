/**
 * 票据相关路由
 */
const express = require('express');
const router = express.Router();
const path = require('path');
const { upload } = require('../middlewares/upload');
const { ticketUploadRules } = require('../middlewares/ticketValidationRules');
const { validate } = require('../middlewares/validator');
const { AppError } = require('../middlewares/error');
const logger = require('../utils/logger');
const { recognizeTicket } = require('../utils/siliconflowAI');
const { createTicket, getTicketList, findTicketById, updateTicket } = require('../models/ticketModel');
const { v4: uuidv4 } = require('uuid');

/**
 * 票据图片上传和识别接口
 * @route POST /api/tickets/upload
 * @param {File} image - 票据图片文件
 * @param {string} [type] - 票据类型（可选）
 * @param {string} [category] - 票据分类（可选）
 * @returns {Object} 包含识别结果和票据信息的响应
 */
router.post('/upload', 
  upload.single('image'), 
  ticketUploadRules, 
  validate, 
  async (req, res, next) => {
    try {
      // 检查是否上传了文件
      if (!req.file) {
        throw new AppError('请上传票据图片', 400);
      }

      // 检查文件类型是否为图片
      const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedImageTypes.includes(req.file.mimetype)) {
        throw new AppError('只支持上传图片文件（JPEG、PNG、GIF、WebP）', 400);
      }

      const { type, category } = req.body;
      const imagePath = req.file.path;
      const imageUrl = `/uploads/${req.file.filename}`;

      logger.info(`开始识别票据图片: ${req.file.filename}`);

      // 调用AI识别票据信息
      const recognitionResult = await recognizeTicket(imagePath);

      // 处理识别结果，确保数据格式正确
      const processedResult = {
        type: type || recognitionResult.type || 'other',
        title: recognitionResult.title || '未识别票据',
        amount: parseFloat(recognitionResult.amount) || 0.00,
        date: recognitionResult.date || new Date().toISOString().split('T')[0],
        description: recognitionResult.description || '',
        category: category || recognitionResult.category || '',
        image_url: imageUrl,
        status: 'pending',
        created_by: uuidv4(), // 临时使用UUID，实际应该从认证中获取用户ID
        // AI识别的额外信息
        ai_recognition: {
          issuer: recognitionResult.issuer || '',
          recipient: recognitionResult.recipient || '',
          tax_amount: parseFloat(recognitionResult.tax_amount) || 0,
          invoice_number: recognitionResult.invoice_number || '',
          confidence: recognitionResult.confidence || 'medium'
        }
      };

      // 保存票据到数据库
      const savedTicket = await createTicket(processedResult);

      logger.info(`票据识别和保存成功: ${savedTicket.id}`);

      // 返回成功响应
      res.success({
        ticket: {
          id: savedTicket.id,
          type: savedTicket.type,
          title: savedTicket.title,
          amount: savedTicket.amount,
          date: savedTicket.date,
          description: savedTicket.description,
          image_url: savedTicket.image_url,
          category: savedTicket.category,
          status: savedTicket.status,
          created_at: savedTicket.created_at
        },
        recognition_result: processedResult.ai_recognition,
        file_info: {
          original_name: req.file.originalname,
          filename: req.file.filename,
          size: req.file.size,
          mimetype: req.file.mimetype
        }
      }, '票据上传和识别成功');

    } catch (error) {
      logger.error(`票据上传识别失败: ${error.message}`);
      
      // 如果上传了文件但处理失败，删除文件
      if (req.file) {
        const fs = require('fs');
        try {
          fs.unlinkSync(req.file.path);
        } catch (deleteError) {
          logger.error(`删除失败文件时出错: ${deleteError.message}`);
        }
      }
      
      next(error);
    }
  }
);

/**
 * 获取票据列表接口
 * @route GET /api/tickets
 * @param {number} page - 页码（默认1）
 * @param {number} pageSize - 每页数量（默认20）
 * @param {string} type - 票据类型筛选
 * @param {string} status - 状态筛选
 * @param {string} category - 分类筛选
 * @param {string} search - 搜索关键词
 * @param {string} sortBy - 排序字段
 * @param {string} sortOrder - 排序顺序
 * @returns {Object} 票据列表和分页信息
 */
router.get('/', async (req, res, next) => {
  try {
    const {
      page = 1,
      current = 1,
      pageSize = 20,
      type,
      status,
      category,
      search,
      keyword,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;
    
    // 支持前端的 current 参数
    const actualPage = current || page;
    const searchTerm = search || keyword;
    
    const options = {
      page: parseInt(actualPage),
      pageSize: parseInt(pageSize),
      type,
      status,
      category,
      search: searchTerm,
      sortBy,
      sortOrder
    };
    
    // 使用模型函数获取票据列表
    const result = await getTicketList(options);
    
    // 匹配前端期望的响应格式
    res.success({
      list: result.data,
      pagination: {
        current: result.pagination.page,
        pageSize: result.pagination.pageSize,
        total: result.pagination.total
      }
    }, '获取票据列表成功');
    
  } catch (error) {
    logger.error(`获取票据列表失败: ${error.message}`);
    next(error);
  }
});

/**
 * 根据ID获取票据详情
 * @route GET /api/tickets/:id
 * @param {string} id - 票据ID
 * @returns {Object} 票据详情
 */
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const ticket = await findTicketById(id);
    
    if (!ticket) {
      throw new AppError('票据不存在', 404);
    }
    
    res.success(ticket, '获取票据详情成功');
    
  } catch (error) {
    logger.error(`获取票据详情失败: ${error.message}`);
    next(error);
  }
});

/**
 * 更新票据信息
 * @route PUT /api/tickets/:id
 * @param {string} id - 票据ID
 * @returns {Object} 更新后的票据信息
 */
router.put('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // 检查票据是否存在
    const existingTicket = await findTicketById(id);
    if (!existingTicket) {
      throw new AppError('票据不存在', 404);
    }
    
    // 更新票据
    const updatedTicket = await updateTicket(id, updateData);
    
    res.success(updatedTicket, '票据更新成功');
    
  } catch (error) {
    logger.error(`更新票据失败: ${error.message}`);
    next(error);
  }
});

module.exports = router;