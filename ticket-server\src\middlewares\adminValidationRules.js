/**
 * 管理员验证规则
 * 定义管理员相关请求的验证规则
 */
const { body, param } = require('express-validator');

/**
 * 管理员登录验证规则
 */
const adminLoginRules = [
  body('username')
    .notEmpty().withMessage('用户名不能为空')
    .isLength({ min: 3 }).withMessage('用户名长度不能少于3个字符'),
  body('password')
    .notEmpty().withMessage('密码不能为空')
    .isLength({ min: 6 }).withMessage('密码长度不能少于6个字符')
];

/**
 * 管理员ID参数验证规则
 */
const adminIdParamRules = [
  param('admin_id')
    .notEmpty().withMessage('管理员ID不能为空')
    .isInt().withMessage('管理员ID必须是整数')
];

module.exports = {
  adminLoginRules,
  adminIdParamRules
};