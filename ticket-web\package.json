{"name": "ticket-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "check": "vue-tsc -b", "lint": "eslint . --ext .ts,.vue", "lint:fix": "eslint . --ext .ts,.vue --fix"}, "dependencies": {"@vueuse/core": "^13.6.0", "axios": "^1.11.0", "clsx": "^2.1.1", "echarts": "^5.6.0", "lucide-vue-next": "^0.511.0", "pinia": "^3.0.3", "tailwind-merge": "^3.3.0", "tesseract.js": "^6.0.1", "vue": "^3.4.15", "vue-echarts": "^7.0.3", "vue-router": "^4.2.5", "vue-sonner": "^1.3.2"}, "devDependencies": {"@types/node": "^22.15.30", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vitejs/plugin-vue": "^5.0.3", "@vue/runtime-dom": "^3.4.15", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "~5.3.3", "unplugin-vue-dev-locator": "^1.0.0", "vite": "^5.0.12", "vite-plugin-trae-solo-badge": "^1.0.0", "vue-tsc": "^1.8.27"}}