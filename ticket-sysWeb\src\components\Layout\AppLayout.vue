<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <aside 
      :class="[
        'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out',
        sidebarCollapsed ? '-translate-x-full' : 'translate-x-0'
      ]"
    >
      <div class="flex items-center justify-center h-16 px-4 border-b border-gray-200">
        <h1 class="text-xl font-bold text-gray-800">校园卡票据助手</h1>
      </div>
      
      <nav class="mt-8">
        <div class="px-4 space-y-2">
          <router-link
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            :class="[
              'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200',
              $route.path === item.path
                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
            ]"
          >
            <component :is="item.icon" class="w-5 h-5 mr-3" />
            {{ item.name }}
          </router-link>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <div :class="['transition-all duration-300', sidebarCollapsed ? 'ml-0' : 'ml-64']">
      <!-- 顶部导航栏 -->
      <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between px-6 py-4">
          <div class="flex items-center">
            <button
              @click="toggleSidebar"
              class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            >
              <Menu class="w-6 h-6" />
            </button>
            
            <!-- 面包屑导航 -->
            <nav class="ml-4">
              <ol class="flex items-center space-x-2">
                <li v-for="(crumb, index) in breadcrumbs" :key="index">
                  <div class="flex items-center">
                    <ChevronRight v-if="index > 0" class="w-4 h-4 text-gray-400 mx-2" />
                    <span 
                      :class="[
                        'text-sm font-medium',
                        index === breadcrumbs.length - 1 ? 'text-gray-900' : 'text-gray-500'
                      ]"
                    >
                      {{ crumb.name }}
                    </span>
                  </div>
                </li>
              </ol>
            </nav>
          </div>

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <button class="p-2 text-gray-400 hover:text-gray-500">
              <Bell class="w-6 h-6" />
            </button>
            
            <!-- 已登录状态显示用户信息 -->
            <div v-if="isAuthenticated" class="relative">
              <button
                @click="showUserMenu = !showUserMenu"
                class="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100"
              >
                <img
                  :src="(admin?.avatar && admin.avatar.trim() !== '') ? admin.avatar : 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20admin%20avatar%20icon&image_size=square'"
                  :alt="admin?.real_name || admin?.username || user?.username"
                  class="w-8 h-8 rounded-full"
                >
                <span class="text-sm font-medium text-gray-700">{{ admin?.real_name || admin?.username || user?.username }}</span>
                <ChevronDown class="w-4 h-4 text-gray-400" />
              </button>
              
              <!-- 用户下拉菜单 -->
              <div
                v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50"
              >
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  个人设置
                </a>
                <button
                  @click="handleLogout"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  退出登录
                </button>
              </div>
            </div>
            
            <!-- 未登录状态显示登录和注册按钮 -->
            <div v-else class="flex items-center space-x-2">
              <router-link 
                to="/login" 
                class="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-800 transition-colors"
              >
                登录
              </router-link>
              <router-link 
                to="/register" 
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
              >
                注册
              </router-link>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6">
        <router-view />
      </main>
    </div>

    <!-- 移动端遮罩层 -->
    <div
      v-if="!sidebarCollapsed"
      @click="toggleSidebar"
      class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  Menu, 
  Home, 
  FileText, 
  Users, 
  Settings, 
  Bell, 
  ChevronDown, 
  ChevronRight 
} from 'lucide-vue-next'
import { useUserStore, useAppStore } from '@/stores'

// 状态管理
const userStore = useUserStore()
const appStore = useAppStore()
const router = useRouter()
const route = useRoute()

// 响应式数据
const showUserMenu = ref(false)

// 计算属性
const user = computed(() => userStore.user)
const admin = computed(() => userStore.admin)
const currentUser = computed(() => admin.value || user.value)
const isAuthenticated = computed(() => userStore.isAuthenticated)
const sidebarCollapsed = computed(() => appStore.sidebarCollapsed)

// 菜单项配置
const menuItems = [
  { name: '首页', path: '/', icon: Home },
  { name: '票据管理', path: '/tickets', icon: FileText },
  { name: '用户管理', path: '/users', icon: Users },
  { name: '系统设置', path: '/settings', icon: Settings }
]

// 面包屑导航
const breadcrumbs = computed(() => {
  const pathSegments = route.path.split('/').filter(Boolean)
  const crumbs = [{ name: '首页', path: '/' }]
  
  let currentPath = ''
  pathSegments.forEach(segment => {
    currentPath += `/${segment}`
    const menuItem = menuItems.find(item => item.path === currentPath)
    if (menuItem) {
      crumbs.push({ name: menuItem.name, path: currentPath })
    }
  })
  
  return crumbs
})

/**
 * 切换侧边栏显示状态
 */
const toggleSidebar = () => {
  appStore.setSidebarCollapsed(!sidebarCollapsed.value)
}

/**
 * 处理用户登出
 */
const handleLogout = async () => {
  try {
    userStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

/**
 * 处理点击外部区域关闭用户菜单
 */
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showUserMenu.value = false
  }
}

/**
 * 响应式处理
 */
const handleResize = () => {
  if (window.innerWidth < 1024) {
    appStore.setSidebarCollapsed(true)
  } else {
    appStore.setSidebarCollapsed(false)
  }
}

// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', handleResize)
  handleResize() // 初始化时检查屏幕尺寸
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', handleResize)
})
</script>