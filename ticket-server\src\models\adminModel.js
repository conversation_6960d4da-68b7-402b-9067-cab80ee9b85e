/**
 * 管理员用户模型
 * 处理管理员用户相关的数据库操作
 */
const mysql = require('mysql2/promise');
const { createPool } = require('../config/db');
const logger = require('../utils/logger');
const { generateSalt, hashPassword, verifyPassword } = require('../utils/crypto');

/**
 * 初始化管理员表
 * 检查管理员表是否存在，如果不存在则创建
 * @returns {Promise<void>}
 */
const initAdminTable = async () => {
  try {
    const pool = await createPool();
    const connection = await pool.getConnection();

    // 检查表是否存在
    const [tables] = await connection.query(
      "SHOW TABLES LIKE 'admin_user'"
    );

    // 如果表不存在，创建表
    if (tables.length === 0) {
      logger.info('创建管理员用户表');
      await connection.query(`
        CREATE TABLE admin_user (
          admin_id INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员唯一ID',
          username VARCHAR(50) NOT NULL COMMENT '登录账号（唯一）',
          password_hash VARCHAR(255) NOT NULL COMMENT '加密后的密码',
          salt VARCHAR(50) NOT NULL COMMENT '密码加密盐值',
          real_name VARCHAR(20) NOT NULL DEFAULT '' COMMENT '真实姓名',
          mobile VARCHAR(15) NOT NULL DEFAULT '' COMMENT '手机号（唯一）',
          email VARCHAR(100) NOT NULL DEFAULT '' COMMENT '邮箱（唯一）',
          avatar VARCHAR(255) NOT NULL DEFAULT '' COMMENT '头像URL',
          role_type ENUM('super_admin', 'content_admin', 'user_admin', 'audit_admin') NOT NULL DEFAULT 'content_admin' COMMENT '角色类型',
          status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用 1-启用 2-锁定',
          last_login_ip VARCHAR(45) NOT NULL DEFAULT '' COMMENT '最后登录IP',
          last_login_time DATETIME DEFAULT NULL COMMENT '最后登录时间',
          creator_id INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者ID（0=系统创建）',
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (admin_id),
          UNIQUE KEY uniq_username (username),
          UNIQUE KEY uniq_mobile (mobile),
          UNIQUE KEY uniq_email (email),
          KEY idx_role_status (role_type,status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表'
      `);

      // 创建初始超级管理员
      await createInitialAdmin(connection);
    }

    connection.release();
  } catch (error) {
    logger.error(`初始化管理员表失败: ${error.message}`);
    throw error;
  }
};

/**
 * 创建初始超级管理员
 * @param {mysql.Connection} connection - 数据库连接
 * @returns {Promise<void>}
 */
const createInitialAdmin = async (connection) => {
  try {
    // 检查是否已存在管理员
    const [admins] = await connection.query('SELECT * FROM admin_user LIMIT 1');
    
    if (admins.length === 0) {
      logger.info('创建初始超级管理员');
      
      // 生成盐值和密码哈希
      const salt = generateSalt();
      const password = 'admin123456'; // 初始密码
      const passwordHash = hashPassword(password, salt);
      
      // 插入超级管理员记录
      await connection.query(`
        INSERT INTO admin_user (
          username, password_hash, salt, real_name, email, role_type, status, creator_id
        ) VALUES (
          'admin', ?, ?, '系统管理员', '<EMAIL>', 'super_admin', 1, 0
        )
      `, [passwordHash, salt]);
      
      logger.info('初始超级管理员创建成功');
    }
  } catch (error) {
    logger.error(`创建初始管理员失败: ${error.message}`);
    throw error;
  }
};

/**
 * 根据用户名查找管理员
 * @param {String} username - 管理员用户名
 * @returns {Promise<Object|null>} 管理员信息或null
 */
const findAdminByUsername = async (username) => {
  try {
    const pool = await createPool();
    const [rows] = await pool.query(
      'SELECT * FROM admin_user WHERE username = ?',
      [username]
    );
    
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    logger.error(`查找管理员失败: ${error.message}`);
    throw error;
  }
};

/**
 * 更新管理员最后登录信息
 * @param {Number} adminId - 管理员ID
 * @param {String} ip - 登录IP
 * @returns {Promise<void>}
 */
const updateAdminLastLogin = async (adminId, ip) => {
  try {
    const pool = await createPool();
    await pool.query(
      'UPDATE admin_user SET last_login_ip = ?, last_login_time = NOW() WHERE admin_id = ?',
      [ip, adminId]
    );
    
    logger.debug(`更新管理员 ${adminId} 的登录信息`);
  } catch (error) {
    logger.error(`更新管理员登录信息失败: ${error.message}`);
    throw error;
  }
};

/**
 * 更新管理员密码
 * @param {String} username - 管理员用户名
 * @param {String} newPassword - 新密码
 * @returns {Promise<boolean>} 更新是否成功
 */
const updateAdminPassword = async (username, newPassword) => {
  try {
    const pool = await createPool();
    
    // 生成新的盐值和密码哈希
    const salt = generateSalt();
    const passwordHash = hashPassword(newPassword, salt);
    
    const [result] = await pool.query(
      'UPDATE admin_user SET password_hash = ?, salt = ?, updated_at = NOW() WHERE username = ?',
      [passwordHash, salt, username]
    );
    
    if (result.affectedRows > 0) {
      logger.info(`管理员 ${username} 密码更新成功`);
      return true;
    } else {
      logger.warn(`管理员 ${username} 不存在`);
      return false;
    }
  } catch (error) {
    logger.error(`更新管理员密码失败: ${error.message}`);
    throw error;
  }
};

module.exports = {
  initAdminTable,
  findAdminByUsername,
  updateAdminLastLogin,
  updateAdminPassword,
  verifyPassword
};