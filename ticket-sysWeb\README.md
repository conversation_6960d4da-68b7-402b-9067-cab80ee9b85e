# 票据助手管理平台

## 项目介绍
后台管理系统网站ticket-sysWeb项目
票据助手管理平台是一个现代化的票据管理系统，旨在帮助企业和个人高效地管理各类票据，包括发票、收据、收入和支出等。系统提供了直观的用户界面和强大的功能，使票据管理变得简单高效。

## 功能特点

- **票据管理**：创建、查看、编辑和删除票据，支持多种票据类型（发票、收据、收入、支出等）
- **票据审批**：完整的票据审批流程，支持多级审批和审批意见
- **数据统计**：直观的数据统计和可视化图表，帮助了解票据趋势和分布
- **用户管理**：管理员可以创建和管理用户账户，分配不同的角色和权限
- **系统设置**：灵活的系统配置，包括邮件设置、数据备份和恢复等

## 技术栈

- **前端框架**：Vue 3 + TypeScript + Vite
- **UI 组件**：Tailwind CSS + Lucide Icons
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **HTTP 客户端**：Axios
- **图表库**：ECharts
- **工具库**：VueUse、dayjs

## 项目结构

```
├── src/                  # 源代码目录
│   ├── api/              # API 接口定义
│   ├── assets/           # 静态资源
│   ├── components/       # 公共组件
│   ├── composables/      # 组合式函数
│   ├── lib/              # 工具库
│   ├── pages/            # 页面组件
│   ├── router/           # 路由配置
│   ├── stores/           # 状态管理
│   ├── types/            # TypeScript 类型定义
│   ├── utils/            # 工具函数
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── public/               # 公共资源目录
├── index.html            # HTML 模板
├── package.json          # 项目依赖和脚本
├── tsconfig.json         # TypeScript 配置
├── vite.config.ts        # Vite 配置
└── tailwind.config.js    # Tailwind CSS 配置
```

## 安装和运行

### 环境要求

- Node.js 16.0 或更高版本
- npm 7.0 或更高版本

### 安装步骤

1. 克隆项目代码

```bash
git clone <repository-url>
cd ticket-sysWeb
```

2. 安装依赖

```bash
npm install
```

3. 启动开发服务器

```bash
npm run dev
```

4. 构建生产版本

```bash
npm run build
```

## 使用指南

### 登录系统

使用管理员账号登录系统，默认账号：
- 用户名：admin
- 密码：admin123

### 票据管理

1. 在首页点击「新建票据」按钮创建新票据
2. 在票据列表页可以查看、编辑和删除票据
3. 使用搜索和筛选功能快速找到需要的票据

### 数据统计

首页提供了票据数量、金额等关键指标的统计信息和趋势图表，帮助您了解票据管理的整体情况。

### 用户管理

管理员可以在「用户管理」页面创建和管理用户账户，分配不同的角色和权限。

### 系统设置

在「设置」页面可以配置系统参数，包括邮件设置、数据备份和恢复等。

## 开发指南

### 添加新页面

1. 在 `src/pages` 目录下创建新的页面组件
2. 在 `src/router/index.ts` 中添加新的路由配置

### 添加新 API

在 `src/api/index.ts` 文件中添加新的 API 接口定义。

### 添加新组件

在 `src/components` 目录下创建新的组件，并在需要的页面中引入使用。

## 贡献指南

欢迎贡献代码、报告问题或提出改进建议。请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

[MIT License](LICENSE)