{"name": "ticket-server", "version": "1.0.0", "description": "后台服务API项目", "main": "src/app.js", "scripts": {"start": "cross-env NODE_ENV=production node src/app.js", "dev": "cross-env NODE_ENV=development nodemon src/app.js", "dev:test": "cross-env NODE_ENV=test nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "mysql", "jwt"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.11.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.6.5", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"cross-env": "^10.0.0", "nodemon": "^3.0.2"}}