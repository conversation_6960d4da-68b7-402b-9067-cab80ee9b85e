/**
 * API速率限制中间件
 */
const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');
const { AppError } = require('./error');

/**
 * 创建速率限制中间件
 * 在开发和测试环境中不启用速率限制
 * 在生产环境中，同一个IP对同一个接口1分钟内最多请求10次
 */
const createRateLimiter = (endpoint) => {
  // 获取当前环境
  const env = process.env.NODE_ENV || 'development';
  
  // 在开发或测试环境中不启用速率限制
  if (env === 'development' || env === 'test') {
    return (req, res, next) => next();
  }
  
  // 在生产环境中启用速率限制
  return rateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 10, // 限制每个IP在windowMs时间内最多10次请求
    standardHeaders: true, // 返回标准的RateLimit头部
    legacyHeaders: false, // 禁用旧版头部
    message: {
      status: 'error',
      statusCode: 429,
      message: `请求过于频繁，请稍后再试`
    },
    keyGenerator: (req) => {
      // 使用IP和路径组合作为限制键，这样可以针对特定接口进行限制
      // 获取真实IP地址，处理代理情况
      const ip = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
      return `${ip}:${endpoint || req.originalUrl}`;
    },
    handler: (req, res, next, options) => {
      const error = new AppError('请求过于频繁，请稍后再试', 429);
      logger.warn(`速率限制触发: ${req.ip} - ${req.method} ${req.originalUrl}`);
      next(error);
    }
  });
};

module.exports = { createRateLimiter };