<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTicketStore } from '@/stores/useTicketStore'
import { TicketStatus, type Ticket, type VerificationResult } from '@/types'
import { 
  Shield, 
  Upload, 
  Search, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Clock, 
  FileText, 
  Download, 
  RefreshCw, 
  Eye, 
  Filter,
  Zap,
  BarChart3,
  Settings
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const router = useRouter()
const ticketStore = useTicketStore()

// 核对模式
const verificationMode = ref<'single' | 'batch'>('single')

// 单个核对
const singleVerification = reactive({
  ticketId: '',
  loading: false,
  result: null as VerificationResult | null
})

// 批量核对
const batchVerification = reactive({
  selectedTickets: [] as string[],
  loading: false,
  results: [] as Array<{ ticketId: string; result: VerificationResult }>,
  progress: 0
})

// 文件上传核对
const fileVerification = reactive({
  files: [] as File[],
  loading: false,
  results: [] as Array<{ fileName: string; result: VerificationResult }>
})

// 筛选条件
const filterOptions = reactive({
  status: '',
  type: '',
  dateRange: {
    start: '',
    end: ''
  }
})

// 核对统计
const verificationStats = ref({
  total: 0,
  verified: 0,
  pending: 0,
  rejected: 0,
  successRate: 0
})

// 显示设置面板
const showSettings = ref(false)

// 核对设置
const verificationSettings = reactive({
  autoVerify: true,
  strictMode: false,
  confidenceThreshold: 0.8,
  enableOCR: true,
  enableImageAnalysis: true
})

/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
  fetchPendingTickets()
  fetchVerificationStats()
})

/**
 * 获取待核对票据
 */
const fetchPendingTickets = async () => {
  await ticketStore.fetchTickets({
    status: TicketStatus.PENDING,
    page: 1,
    pageSize: 50
  })
}

/**
 * 获取核对统计数据
 */
const fetchVerificationStats = async () => {
  try {
    const stats = await ticketStore.getVerificationStats()
    verificationStats.value = stats
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

/**
 * 单个票据核对
 */
const verifySingleTicket = async () => {
  if (!singleVerification.ticketId.trim()) {
    toast.error('请输入票据ID')
    return
  }
  
  try {
    singleVerification.loading = true
    const success = await ticketStore.verifyTicket(singleVerification.ticketId, true)
    
    // 创建验证结果对象
    const result: VerificationResult = {
      ticketId: singleVerification.ticketId,
      isValid: success,
      confidence: 0.95,
      verifiedAt: new Date().toISOString(),
      verifiedBy: 'system',
      issues: []
    }
    
    singleVerification.result = result
    
    if (result.isValid) {
      toast.success('票据核对通过')
    } else {
      toast.error('票据核对未通过')
    }
  } catch (error) {
    toast.error('核对失败，请检查票据ID是否正确')
    singleVerification.result = null
  } finally {
    singleVerification.loading = false
  }
}

/**
 * 批量票据核对
 */
const verifyBatchTickets = async () => {
  if (batchVerification.selectedTickets.length === 0) {
    toast.error('请选择要核对的票据')
    return
  }
  
  try {
    batchVerification.loading = true
    batchVerification.progress = 0
    batchVerification.results = []
    
    const total = batchVerification.selectedTickets.length
    
    for (let i = 0; i < total; i++) {
      const ticketId = batchVerification.selectedTickets[i]
      
      try {
        const success = await ticketStore.verifyTicket(ticketId, true)
        
        // 创建验证结果对象
        const result: VerificationResult = {
          ticketId: ticketId,
          isValid: success,
          confidence: 0.95,
          verifiedAt: new Date().toISOString(),
          verifiedBy: 'system',
          issues: []
        }
        
        batchVerification.results.push({ ticketId, result })
      } catch (error) {
        console.error(`票据 ${ticketId} 核对失败:`, error)
      }
      
      batchVerification.progress = Math.round(((i + 1) / total) * 100)
      
      // 模拟处理延迟
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    const successCount = batchVerification.results.filter(r => r.result.isValid).length
    toast.success(`批量核对完成，${successCount}/${total} 个票据通过验证`)
    
    // 刷新票据列表和统计数据
    await fetchPendingTickets()
    await fetchVerificationStats()
    
  } catch (error) {
    toast.error('批量核对失败')
  } finally {
    batchVerification.loading = false
  }
}

/**
 * 文件上传处理
 * @param event 文件上传事件
 */
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    fileVerification.files = Array.from(target.files)
  }
}

/**
 * 文件核对
 */
const verifyFiles = async () => {
  if (fileVerification.files.length === 0) {
    toast.error('请选择要核对的文件')
    return
  }
  
  try {
    fileVerification.loading = true
    fileVerification.results = []
    
    for (const file of fileVerification.files) {
      // 模拟文件核对
      const result: VerificationResult = {
        ticketId: file.name,
        isValid: Math.random() > 0.3,
        confidence: Math.random() * 0.4 + 0.6,
        verifiedAt: new Date().toISOString(),
        issues: Math.random() > 0.7 ? ['图片质量较低', '部分信息不清晰'] : []
      }
      
      fileVerification.results.push({
        fileName: file.name,
        result
      })
      
      // 模拟处理延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
    
    const successCount = fileVerification.results.filter(r => r.result.isValid).length
    toast.success(`文件核对完成，${successCount}/${fileVerification.files.length} 个文件通过验证`)
    
  } catch (error) {
    toast.error('文件核对失败')
  } finally {
    fileVerification.loading = false
  }
}

/**
 * 切换票据选择
 * @param ticketId 票据ID
 */
const toggleTicketSelection = (ticketId: string) => {
  const index = batchVerification.selectedTickets.indexOf(ticketId)
  if (index > -1) {
    batchVerification.selectedTickets.splice(index, 1)
  } else {
    batchVerification.selectedTickets.push(ticketId)
  }
}

/**
 * 全选/取消全选
 */
const toggleSelectAll = () => {
  if (batchVerification.selectedTickets.length === pendingTickets.value.length) {
    batchVerification.selectedTickets = []
  } else {
    batchVerification.selectedTickets = pendingTickets.value.map(t => t.id)
  }
}

/**
 * 导出核对结果
 */
const exportResults = () => {
  if (batchVerification.results.length === 0 && fileVerification.results.length === 0) {
    toast.error('暂无核对结果可导出')
    return
  }
  
  // 模拟导出功能
  toast.success('正在导出核对结果...')
}

/**
 * 查看票据详情
 * @param ticketId 票据ID
 */
const viewTicketDetail = (ticketId: string) => {
  router.push(`/tickets/${ticketId}`)
}

/**
 * 格式化日期时间
 * @param date 日期字符串
 */
const formatDateTime = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

/**
 * 获取核对结果样式
 * @param isValid 是否有效
 */
const getResultClass = (isValid: boolean) => {
  return isValid 
    ? 'bg-green-100 text-green-800 border-green-200'
    : 'bg-red-100 text-red-800 border-red-200'
}

// 计算属性
const pendingTickets = computed(() => {
  return ticketStore.tickets.filter(ticket => {
    if (filterOptions.status && ticket.status !== filterOptions.status) return false
    if (filterOptions.type && ticket.type !== filterOptions.type) return false
    if (filterOptions.dateRange.start && ticket.date < filterOptions.dateRange.start) return false
    if (filterOptions.dateRange.end && ticket.date > filterOptions.dateRange.end) return false
    return true
  })
})

const isAllSelected = computed(() => {
  return pendingTickets.value.length > 0 && 
         batchVerification.selectedTickets.length === pendingTickets.value.length
})

const hasResults = computed(() => {
  return batchVerification.results.length > 0 || fileVerification.results.length > 0
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">票据核对</h1>
        <p class="text-gray-600">验证票据真伪和完整性</p>
      </div>
      <div class="mt-4 sm:mt-0 flex items-center space-x-2">
        <button 
          @click="showSettings = !showSettings"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <Settings class="w-4 h-4 mr-2" />
          设置
        </button>
        <button 
          v-if="hasResults"
          @click="exportResults"
          class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          <Download class="w-4 h-4 mr-2" />
          导出结果
        </button>
      </div>
    </div>

    <!-- 核对统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <BarChart3 class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">总票据数</div>
            <div class="text-2xl font-bold text-gray-900">{{ verificationStats.total }}</div>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <CheckCircle class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">已验证</div>
            <div class="text-2xl font-bold text-gray-900">{{ verificationStats.verified }}</div>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Clock class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">待处理</div>
            <div class="text-2xl font-bold text-gray-900">{{ verificationStats.pending }}</div>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Zap class="h-8 w-8 text-purple-600" />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">成功率</div>
            <div class="text-2xl font-bold text-gray-900">{{ verificationStats.successRate }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 核对设置面板 -->
    <div v-if="showSettings" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">核对设置</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <label class="text-sm font-medium text-gray-700">自动核对</label>
            <input 
              v-model="verificationSettings.autoVerify"
              type="checkbox" 
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
          </div>
          <div class="flex items-center justify-between">
            <label class="text-sm font-medium text-gray-700">严格模式</label>
            <input 
              v-model="verificationSettings.strictMode"
              type="checkbox" 
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
          </div>
          <div class="flex items-center justify-between">
            <label class="text-sm font-medium text-gray-700">启用OCR识别</label>
            <input 
              v-model="verificationSettings.enableOCR"
              type="checkbox" 
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
          </div>
        </div>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">置信度阈值</label>
            <input 
              v-model.number="verificationSettings.confidenceThreshold"
              type="range" 
              min="0.5" 
              max="1" 
              step="0.1"
              class="w-full"
            >
            <div class="text-xs text-gray-500 mt-1">{{ (verificationSettings.confidenceThreshold * 100).toFixed(0) }}%</div>
          </div>
          <div class="flex items-center justify-between">
            <label class="text-sm font-medium text-gray-700">启用图像分析</label>
            <input 
              v-model="verificationSettings.enableImageAnalysis"
              type="checkbox" 
              class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 核对模式选择 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center space-x-4 mb-6">
        <button 
          @click="verificationMode = 'single'"
          :class="[
            'px-4 py-2 rounded-md transition-colors',
            verificationMode === 'single' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          ]"
        >
          单个核对
        </button>
        <button 
          @click="verificationMode = 'batch'"
          :class="[
            'px-4 py-2 rounded-md transition-colors',
            verificationMode === 'batch' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          ]"
        >
          批量核对
        </button>
      </div>

      <!-- 单个核对 -->
      <div v-if="verificationMode === 'single'" class="space-y-6">
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">单个票据核对</h3>
          <div class="flex space-x-4">
            <div class="flex-1">
              <input 
                v-model="singleVerification.ticketId"
                type="text" 
                placeholder="请输入票据ID"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                @keyup.enter="verifySingleTicket"
              >
            </div>
            <button 
              @click="verifySingleTicket"
              :disabled="singleVerification.loading"
              class="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Shield v-if="!singleVerification.loading" class="w-4 h-4 mr-2" />
              <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
              {{ singleVerification.loading ? '核对中...' : '开始核对' }}
            </button>
          </div>
        </div>
        
        <!-- 单个核对结果 -->
        <div v-if="singleVerification.result" class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-center justify-between mb-4">
            <h4 class="font-medium text-gray-900">核对结果</h4>
            <span 
              :class="[
                'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border',
                getResultClass(singleVerification.result.isValid)
              ]"
            >
              <CheckCircle v-if="singleVerification.result.isValid" class="w-4 h-4 mr-1" />
              <XCircle v-else class="w-4 h-4 mr-1" />
              {{ singleVerification.result.isValid ? '验证通过' : '验证失败' }}
            </span>
          </div>
          
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">置信度：</span>
              <span class="font-medium">{{ (singleVerification.result.confidence * 100).toFixed(1) }}%</span>
            </div>
            <div>
              <span class="text-gray-600">核对时间：</span>
              <span class="font-medium">{{ formatDateTime(singleVerification.result.verifiedAt) }}</span>
            </div>
          </div>
          
          <div v-if="singleVerification.result.issues && singleVerification.result.issues.length > 0" class="mt-4">
            <div class="text-sm font-medium text-gray-900 mb-2">发现问题：</div>
            <ul class="space-y-1">
              <li 
                v-for="issue in singleVerification.result.issues" 
                :key="issue"
                class="text-sm text-red-600 flex items-start"
              >
                <AlertTriangle class="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" />
                {{ issue }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 批量核对 -->
      <div v-else class="space-y-6">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">批量票据核对</h3>
          <div class="flex items-center space-x-2">
            <button 
              @click="toggleSelectAll"
              class="text-sm text-blue-600 hover:text-blue-800"
            >
              {{ isAllSelected ? '取消全选' : '全选' }}
            </button>
            <span class="text-sm text-gray-600">
              已选择 {{ batchVerification.selectedTickets.length }} 个票据
            </span>
          </div>
        </div>
        
        <!-- 筛选条件 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <select 
            v-model="filterOptions.status"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部状态</option>
            <option value="pending">待处理</option>
            <option value="verified">已验证</option>
            <option value="rejected">已拒绝</option>
          </select>
          
          <select 
            v-model="filterOptions.type"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部类型</option>
            <option value="invoice">发票</option>
            <option value="receipt">收据</option>
            <option value="check">支票</option>
            <option value="other">其他</option>
          </select>
          
          <button 
            @click="verifyBatchTickets"
            :disabled="batchVerification.loading || batchVerification.selectedTickets.length === 0"
            class="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Shield v-if="!batchVerification.loading" class="w-4 h-4 mr-2" />
            <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
            {{ batchVerification.loading ? '核对中...' : '开始批量核对' }}
          </button>
        </div>
        
        <!-- 进度条 -->
        <div v-if="batchVerification.loading" class="w-full bg-gray-200 rounded-full h-2">
          <div 
            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${batchVerification.progress}%` }"
          ></div>
          <div class="text-sm text-gray-600 mt-1 text-center">
            {{ batchVerification.progress }}% 完成
          </div>
        </div>
        
        <!-- 票据列表 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <div class="max-h-96 overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50 sticky top-0">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input 
                      type="checkbox" 
                      :checked="isAllSelected"
                      @change="toggleSelectAll"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    >
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    票据信息
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    类型
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr 
                  v-for="ticket in pendingTickets" 
                  :key="ticket.id"
                  class="hover:bg-gray-50"
                >
                  <td class="px-6 py-4 whitespace-nowrap">
                    <input 
                      type="checkbox" 
                      :checked="batchVerification.selectedTickets.includes(ticket.id)"
                      @change="toggleTicketSelection(ticket.id)"
                      class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    >
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm font-medium text-gray-900">{{ ticket.title }}</div>
                    <div class="text-sm text-gray-500">ID: {{ ticket.id }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ ticket.type }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      {{ ticket.status }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button 
                      @click="viewTicketDetail(ticket.id)"
                      class="text-blue-600 hover:text-blue-900"
                    >
                      <Eye class="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件上传核对 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">文件上传核对</h3>
      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <input 
            type="file" 
            multiple 
            accept="image/*,.pdf"
            @change="handleFileUpload"
            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          >
          <button 
            @click="verifyFiles"
            :disabled="fileVerification.loading || fileVerification.files.length === 0"
            class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Upload v-if="!fileVerification.loading" class="w-4 h-4 mr-2" />
            <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
            {{ fileVerification.loading ? '核对中...' : '开始核对' }}
          </button>
        </div>
        
        <div v-if="fileVerification.files.length > 0" class="text-sm text-gray-600">
          已选择 {{ fileVerification.files.length }} 个文件
        </div>
      </div>
    </div>

    <!-- 核对结果 -->
    <div v-if="hasResults" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">核对结果</h3>
        <button 
          @click="exportResults"
          class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          <Download class="w-4 h-4 mr-2" />
          导出结果
        </button>
      </div>
      
      <!-- 批量核对结果 -->
      <div v-if="batchVerification.results.length > 0" class="mb-6">
        <h4 class="font-medium text-gray-900 mb-3">批量核对结果</h4>
        <div class="space-y-2">
          <div 
            v-for="result in batchVerification.results" 
            :key="result.ticketId"
            class="flex items-center justify-between p-3 border border-gray-200 rounded-md"
          >
            <div class="flex items-center space-x-3">
              <CheckCircle v-if="result.result.isValid" class="w-5 h-5 text-green-600" />
              <XCircle v-else class="w-5 h-5 text-red-600" />
              <span class="font-medium">{{ result.ticketId }}</span>
            </div>
            <div class="text-sm text-gray-600">
              置信度: {{ (result.result.confidence * 100).toFixed(1) }}%
            </div>
          </div>
        </div>
      </div>
      
      <!-- 文件核对结果 -->
      <div v-if="fileVerification.results.length > 0">
        <h4 class="font-medium text-gray-900 mb-3">文件核对结果</h4>
        <div class="space-y-2">
          <div 
            v-for="result in fileVerification.results" 
            :key="result.fileName"
            class="flex items-center justify-between p-3 border border-gray-200 rounded-md"
          >
            <div class="flex items-center space-x-3">
              <CheckCircle v-if="result.result.isValid" class="w-5 h-5 text-green-600" />
              <XCircle v-else class="w-5 h-5 text-red-600" />
              <span class="font-medium">{{ result.fileName }}</span>
            </div>
            <div class="text-sm text-gray-600">
              置信度: {{ (result.result.confidence * 100).toFixed(1) }}%
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>