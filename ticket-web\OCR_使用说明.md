# 票据OCR识别功能使用说明

## 功能概述

票据录入页面现已集成 Tesseract.js OCR 图片识别功能 <mcreference link="https://github.com/naptha/tesseract.js" index="1">1</mcreference>，可以自动识别上传的票据图片中的文字信息，并智能填充到表单中。

## 支持的功能

### 1. 多语言识别
- 支持简体中文和英文混合识别
- 使用语言模型：`chi_sim+eng`

### 2. 智能信息提取
- **金额识别**：自动识别各种金额格式（￥、¥、$符号）
- **日期识别**：支持多种日期格式（YYYY年MM月DD日、YYYY-MM-DD、YYYY/MM/DD）
- **票据类型识别**：自动识别发票、收据、支票等类型
- **分类识别**：根据关键词自动分类（餐饮、交通、住宿、办公用品等）

### 3. 智能解析规则

#### 金额提取
- 识别包含 ￥、¥、$ 符号的金额
- 支持千分位分隔符（逗号）
- 自动选择识别到的最大金额作为票据金额

#### 日期提取
- 格式：YYYY年MM月DD日
- 格式：YYYY-MM-DD
- 格式：YYYY/MM/DD
- 自动转换为标准日期格式

#### 票据类型识别
- 包含"发票"关键词 → 发票类型
- 包含"收据"关键词 → 收据类型
- 包含"支票"关键词 → 支票类型

#### 分类关键词匹配
- **餐饮**：餐厅、饭店、食堂、咖啡、茶、酒店餐饮
- **交通**：出租车、地铁、公交、火车、飞机、高铁、滴滴、uber
- **住宿**：酒店、宾馆、民宿、旅馆
- **办公用品**：文具、纸张、笔、办公、打印
- **通讯**：电话费、手机、网络、流量
- **水电费**：水费、电费、燃气费、物业费

## 使用步骤

1. **访问票据录入页面**
   - 打开 `http://localhost:5175/create`

2. **上传票据图片**
   - 点击上传区域选择图片文件
   - 支持 JPG、PNG 格式
   - 文件大小限制：5MB

3. **等待识别完成**
   - 系统会显示识别进度
   - 识别过程可能需要几秒到几十秒
   - 识别完成后会显示成功提示

4. **检查和完善信息**
   - 系统会自动填充识别到的信息
   - 请仔细检查识别结果的准确性
   - 手动修正或补充遗漏的信息

5. **保存票据**
   - 确认信息无误后点击保存

## 注意事项

### 图片质量要求
- **清晰度**：图片应清晰，文字可读
- **光线**：避免过暗或过亮的图片
- **角度**：尽量保持票据正向，避免倾斜
- **完整性**：确保票据信息完整，无遮挡

### 识别准确性
- OCR识别准确率受图片质量影响 <mcreference link="https://cloud.tencent.com/developer/article/2184369" index="4">4</mcreference>
- 建议上传高质量、清晰的票据图片
- 识别结果仅供参考，请务必人工核验
- 复杂背景或手写文字可能影响识别效果

### 性能说明
- 首次使用时需要下载语言模型，可能较慢
- 识别速度取决于图片大小和复杂度
- 建议在网络良好的环境下使用

## 技术实现

- **OCR引擎**：Tesseract.js v5+ <mcreference link="https://github.com/naptha/tesseract.js" index="1">1</mcreference>
- **语言支持**：简体中文 + 英文
- **运行环境**：浏览器端，无需服务器支持
- **数据安全**：图片在本地处理，不上传到服务器

## 故障排除

### 识别失败
- 检查图片格式是否支持
- 确认图片大小未超过限制
- 尝试重新上传图片
- 检查网络连接状态

### 识别结果不准确
- 尝试上传更清晰的图片
- 调整图片角度和光线
- 手动修正识别结果

### 识别速度慢
- 首次使用需要下载模型文件
- 大尺寸图片识别时间较长
- 建议压缩图片后再上传

---

*更新时间：2024年12月*