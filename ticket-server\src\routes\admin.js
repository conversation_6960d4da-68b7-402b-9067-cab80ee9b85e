/**
 * 管理员相关路由
 */
const express = require('express');
const router = express.Router();
const { generateToken } = require('../utils/jwt');
const { adminLoginRules } = require('../middlewares/adminValidationRules');
const { validate } = require('../middlewares/validator');
const { AppError } = require('../middlewares/error');
const logger = require('../utils/logger');
const { findAdminByUsername, updateAdminLastLogin, verifyPassword } = require('../models/adminModel');
const { getAllUsers, getUserById, updateUserStatus, deleteUser } = require('../models/userModel');
const { authenticateJWT } = require('../middlewares/auth');

/**
 * 管理员登录路由
 * @route POST /api/admin/login
 * @param {string} username - 管理员用户名
 * @param {string} password - 管理员密码
 * @returns {Object} 包含token和管理员信息的响应
 */
router.post('/login', adminLoginRules, validate, async (req, res, next) => {
  try {
    const { username, password } = req.body;
    
    logger.info(`管理员 ${username} 尝试登录`);
    
    // 查找管理员
    const admin = await findAdminByUsername(username);
    
    // 验证管理员是否存在
    if (!admin) {
      throw new AppError('用户名或密码错误', 401);
    }
    
    // 验证管理员状态
    if (admin.status === 0) {
      throw new AppError('账号已被禁用', 403);
    }
    
    if (admin.status === 2) {
      throw new AppError('账号已被锁定', 403);
    }
    
    // 验证密码
    const isPasswordValid = verifyPassword(password, admin.password_hash, admin.salt);
    
    if (!isPasswordValid) {
      throw new AppError('用户名或密码错误', 401);
    }
    
    // 更新最后登录信息
    await updateAdminLastLogin(admin.admin_id, req.ip);
    
    // 生成JWT令牌
    const tokenPayload = {
      id: admin.admin_id,
      username: admin.username,
      role: admin.role_type,
      isAdmin: true
    };
    
    const token = generateToken(tokenPayload);
    
    // 准备返回的管理员信息（排除敏感字段）
    const adminInfo = {
      admin_id: admin.admin_id,
      username: admin.username,
      real_name: admin.real_name,
      email: admin.email,
      mobile: admin.mobile,
      avatar: admin.avatar,
      role_type: admin.role_type,
      status: admin.status,
      last_login_time: admin.last_login_time
    };
    
    // 返回令牌和管理员信息
    res.success({
      token,
      admin: adminInfo
    }, '登录成功');
  } catch (error) {
    logger.error(`管理员登录失败: ${error.message}`);
    next(error);
  }
});

/**
 * 管理员获取用户列表
 * @route GET /api/admin/users
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @param {string} search - 搜索关键词
 * @param {number} status - 状态筛选
 * @returns {Object} 用户列表和分页信息
 */
router.get('/users', authenticateJWT, async (req, res, next) => {
  try {
    // 验证管理员权限
    if (!req.user.isAdmin) {
      throw new AppError('权限不足', 403);
    }

    logger.info(`管理员 ${req.user.username} 获取用户列表`);
    
    // 获取查询参数
    const {
      page = 1,
      limit = 10,
      search = '',
      status = null
    } = req.query;

    // 从数据库获取用户列表
    const result = await getAllUsers({
      page: parseInt(page),
      limit: parseInt(limit),
      search,
      status: status ? parseInt(status) : null
    });

    res.success(result, '获取用户列表成功');
  } catch (error) {
    logger.error(`管理员获取用户列表失败: ${error.message}`);
    next(error);
  }
});

/**
 * 管理员获取指定用户详情
 * @route GET /api/admin/users/:id
 * @param {number} id - 用户ID
 * @returns {Object} 用户详细信息
 */
router.get('/users/:id', authenticateJWT, async (req, res, next) => {
  try {
    // 验证管理员权限
    if (!req.user.isAdmin) {
      throw new AppError('权限不足', 403);
    }

    const userId = req.params.id;
    logger.info(`管理员 ${req.user.username} 获取用户信息: ${userId}`);
    
    // 从数据库获取指定用户的信息
    const user = await getUserById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    res.success(user, '获取用户信息成功');
  } catch (error) {
    logger.error(`管理员获取用户信息失败: ${error.message}`);
    next(error);
  }
});

/**
 * 管理员更新用户状态
 * @route PUT /api/admin/users/:id/status
 * @param {number} id - 用户ID
 * @param {number} status - 新状态：0-禁用 1-正常 2-未激活
 * @returns {Object} 操作结果
 */
router.put('/users/:id/status', authenticateJWT, async (req, res, next) => {
  try {
    // 验证管理员权限
    if (!req.user.isAdmin) {
      throw new AppError('权限不足', 403);
    }

    const userId = req.params.id;
    const { status } = req.body;

    // 验证状态值
    if (![0, 1, 2].includes(parseInt(status))) {
      throw new AppError('无效的状态值', 400);
    }

    logger.info(`管理员 ${req.user.username} 更新用户 ${userId} 状态为: ${status}`);
    
    // 检查用户是否存在
    const user = await getUserById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    // 更新用户状态
    await updateUserStatus(userId, parseInt(status));

    res.success(null, '用户状态更新成功');
  } catch (error) {
    logger.error(`管理员更新用户状态失败: ${error.message}`);
    next(error);
  }
});

/**
 * 管理员删除用户（软删除）
 * @route DELETE /api/admin/users/:id
 * @param {number} id - 用户ID
 * @returns {Object} 操作结果
 */
router.delete('/users/:id', authenticateJWT, async (req, res, next) => {
  try {
    // 验证管理员权限
    if (!req.user.isAdmin) {
      throw new AppError('权限不足', 403);
    }

    const userId = req.params.id;
    logger.info(`管理员 ${req.user.username} 删除用户: ${userId}`);
    
    // 检查用户是否存在
    const user = await getUserById(userId);
    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    // 软删除用户（设置状态为禁用）
    await deleteUser(userId);

    res.success(null, '用户删除成功');
  } catch (error) {
    logger.error(`管理员删除用户失败: ${error.message}`);
    next(error);
  }
});

module.exports = router;