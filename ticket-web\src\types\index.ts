// 票据类型枚举
export enum TicketType {
  INVOICE = 'invoice', // 发票
  RECEIPT = 'receipt', // 收据
  CHECK = 'check', // 支票
  CONTRACT = 'contract', // 合同
  OTHER = 'other' // 其他
}

// 票据状态枚举
export enum TicketStatus {
  PENDING = 'pending', // 待处理
  VERIFIED = 'verified', // 已验证
  REJECTED = 'rejected' // 已拒绝
}

// 票据接口
export interface Ticket {
  id: string
  type: TicketType
  title: string
  amount: number
  date: string
  description?: string
  imageUrl?: string
  category?: string
  tags?: string[]
  status: TicketStatus
  createdAt: string
  updatedAt: string
  createdBy?: string
  verificationResult?: VerificationResult
  attachments?: Array<{
    id: string
    name: string
    url: string
    type: string
    size: number
  }>
}

// 票据统计数据接口
export interface TicketStats {
  total: number
  verified: number
  pending: number
  rejected: number
  totalAmount: number
  averageAmount: number
}

// 票据核对结果接口
export interface VerificationResult {
  id?: string
  ticketId: string
  isValid: boolean
  confidence?: number
  issues?: string[]
  verifiedAt: string
  verifiedBy?: string
  details?: {
    taxNumber?: boolean
    amount?: boolean
    date?: boolean
    issuer?: boolean
  }
  message?: string
}

// 用户接口
export interface User {
  id: string
  name: string
  email: string
  role: 'user' | 'enterprise'
  avatar?: string
  phone?: string
  department?: string
  position?: string
  isActive?: boolean
  createdAt?: string
  updatedAt?: string
  lastLoginAt?: string
}

// 登录请求接口
export interface LoginRequest {
  email: string
  password: string
}

// 登录响应接口
export interface LoginResponse {
  user: User
  token: string
  expiresIn: number
}

// 创建用户请求接口
export interface CreateUserRequest {
  name: string
  email: string
  password: string
  role: 'user' | 'enterprise'
  phone?: string
  department?: string
  position?: string
}

// 更新用户请求接口
export interface UpdateUserRequest {
  name?: string
  email?: string
  role?: 'user' | 'enterprise'
  phone?: string
  department?: string
  position?: string
  isActive?: boolean
}

// 用户查询参数接口
export interface UserQueryParams extends PaginationParams {
  keyword?: string
  role?: 'user' | 'enterprise'
  isActive?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: {
    data: T
    total?: number
    page?: number
    pageSize?: number
  }
}

// 分页参数接口
export interface PaginationParams {
  page: number
  pageSize: number
}

// 票据查询参数接口
export interface TicketQueryParams extends PaginationParams {
  keyword?: string
  type?: TicketType
  status?: TicketStatus
  startDate?: string
  endDate?: string
  minAmount?: number
  maxAmount?: number
  category?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}