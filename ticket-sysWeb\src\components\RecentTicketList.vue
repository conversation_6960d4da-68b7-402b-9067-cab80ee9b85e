<template>
  <div>
    <!-- 加载状态 -->
    <div v-if="loading" class="space-y-4">
      <div v-for="i in 3" :key="i" class="animate-pulse">
        <div class="flex items-center space-x-4">
          <div class="w-10 h-10 bg-gray-200 rounded-lg"></div>
          <div class="flex-1 space-y-2">
            <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div class="w-20 h-6 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!tickets.length" class="text-center py-12">
      <FileX class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无票据</h3>
      <p class="text-gray-500 mb-4">您还没有创建任何票据</p>
      <router-link
        to="/tickets/create"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
      >
        <Plus class="w-4 h-4 mr-2" />
        创建第一个票据
      </router-link>
    </div>

    <!-- 票据列表 -->
    <div v-else class="space-y-4">
      <div
        v-for="ticket in tickets"
        :key="ticket.id"
        class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
        @click="navigateToTicket(ticket.id)"
      >
        <div class="flex items-center space-x-4">
          <!-- 票据类型图标 -->
          <div
            :class="[
              'flex items-center justify-center w-10 h-10 rounded-lg',
              getTypeIconBg(ticket.type)
            ]"
          >
            <component
              :is="getTypeIcon(ticket.type)"
              :class="['w-5 h-5', getTypeIconColor(ticket.type)]"
            />
          </div>

          <!-- 票据信息 -->
          <div class="flex-1">
            <h4 class="text-sm font-medium text-gray-900 mb-1">{{ ticket.title }}</h4>
            <div class="flex items-center space-x-4 text-xs text-gray-500">
              <span>{{ getTypeLabel(ticket.type) }}</span>
              <span>{{ formatDate(ticket.createdAt) }}</span>
              <span>创建者: {{ ticket.createdBy }}</span>
            </div>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <!-- 金额 -->
          <div class="text-right">
            <div class="text-sm font-medium text-gray-900">
              {{ formatCurrency(ticket.amount) }}
            </div>
          </div>

          <!-- 状态标签 -->
          <span
            :class="[
              'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
              getStatusColor(ticket.status)
            ]"
          >
            {{ getStatusLabel(ticket.status) }}
          </span>

          <!-- 操作按钮 -->
          <div class="flex items-center space-x-2">
            <button
              @click.stop="editTicket(ticket.id)"
              class="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
              title="编辑"
            >
              <Edit class="w-4 h-4" />
            </button>
            <button
              @click.stop="viewTicket(ticket.id)"
              class="p-1 text-gray-400 hover:text-green-600 transition-colors duration-200"
              title="查看详情"
            >
              <Eye class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  FileX,
  Plus,
  Edit,
  Eye,
  FileText,
  Receipt,
  CreditCard,
  File
} from 'lucide-vue-next'
import type { Ticket, TicketType, TicketStatus } from '@/types'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'

// 配置 dayjs
dayjs.locale('zh-cn')
dayjs.extend(relativeTime)

// 定义组件属性
interface Props {
  tickets: Ticket[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 路由
const router = useRouter()

/**
 * 获取票据类型图标
 * @param type 票据类型
 * @returns 图标组件
 */
const getTypeIcon = (type: TicketType) => {
  const iconMap = {
    invoice: FileText,
    receipt: Receipt,
    expense: CreditCard,
    other: File
  }
  return iconMap[type] || File
}

/**
 * 获取票据类型图标背景色
 * @param type 票据类型
 * @returns CSS 类名
 */
const getTypeIconBg = (type: TicketType): string => {
  const bgMap = {
    invoice: 'bg-blue-50',
    receipt: 'bg-green-50',
    expense: 'bg-yellow-50',
    other: 'bg-gray-50'
  }
  return bgMap[type] || 'bg-gray-50'
}

/**
 * 获取票据类型图标颜色
 * @param type 票据类型
 * @returns CSS 类名
 */
const getTypeIconColor = (type: TicketType): string => {
  const colorMap = {
    invoice: 'text-blue-600',
    receipt: 'text-green-600',
    expense: 'text-yellow-600',
    other: 'text-gray-600'
  }
  return colorMap[type] || 'text-gray-600'
}

/**
 * 获取票据类型标签
 * @param type 票据类型
 * @returns 类型标签
 */
const getTypeLabel = (type: TicketType): string => {
  const labelMap = {
    invoice: '发票',
    receipt: '收据',
    expense: '费用',
    other: '其他'
  }
  return labelMap[type] || '其他'
}

/**
 * 获取状态标签
 * @param status 票据状态
 * @returns 状态标签
 */
const getStatusLabel = (status: TicketStatus): string => {
  const labelMap = {
    draft: '草稿',
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return labelMap[status] || '未知'
}

/**
 * 获取状态颜色
 * @param status 票据状态
 * @returns CSS 类名
 */
const getStatusColor = (status: TicketStatus): string => {
  const colorMap = {
    draft: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colorMap[status] || 'bg-gray-100 text-gray-800'
}

/**
 * 格式化日期
 * @param date 日期字符串
 * @returns 格式化后的日期
 */
const formatDate = (date: string): string => {
  return dayjs(date).fromNow()
}

/**
 * 格式化货币
 * @param amount 金额
 * @returns 格式化后的货币字符串
 */
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

/**
 * 导航到票据详情页
 * @param ticketId 票据 ID
 */
const navigateToTicket = (ticketId: string) => {
  router.push(`/tickets/${ticketId}`)
}

/**
 * 编辑票据
 * @param ticketId 票据 ID
 */
const editTicket = (ticketId: string) => {
  router.push(`/tickets/${ticketId}/edit`)
}

/**
 * 查看票据详情
 * @param ticketId 票据 ID
 */
const viewTicket = (ticketId: string) => {
  router.push(`/tickets/${ticketId}`)
}
</script>