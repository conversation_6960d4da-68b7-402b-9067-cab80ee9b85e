/**
 * 认证中间件
 * 用于验证请求中的JWT令牌
 */
const { verifyToken } = require('../utils/jwt');
const { AppError } = require('./error');
const logger = require('../utils/logger');

/**
 * 验证JWT令牌中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const authenticateJWT = (req, res, next) => {
  try {
    // 从请求头中获取Authorization
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      logger.warn(`请求未提供认证令牌: ${req.originalUrl}`);
      throw new AppError('未提供认证令牌', 401);
    }
    
    // 提取令牌（去掉'Bearer '前缀）
    const token = authHeader.split(' ')[1];
    
    if (!token) {
      logger.warn(`请求提供的认证头格式不正确: ${req.originalUrl}`);
      throw new AppError('认证头格式不正确', 401);
    }
    
    // 验证令牌
    const user = verifyToken(token);
    
    if (!user) {
      logger.warn(`无效的令牌: ${req.originalUrl}`);
      throw new AppError('无效的令牌', 401);
    }
    
    // 将用户信息添加到请求对象中
    req.user = user;
    logger.debug(`用户 ${user.id || 'unknown'} 已认证`);
    next();
  } catch (error) {
    next(error);
  }
};

/**
 * 角色验证中间件
 * @param {...String} roles - 允许访问的角色列表
 * @returns {Function} Express中间件函数
 */
const authorizeRoles = (...roles) => {
  return (req, res, next) => {
    try {
      // 确保用户已经通过了认证
      if (!req.user) {
        logger.warn(`未经认证的用户尝试访问需要角色的路由: ${req.originalUrl}`);
        throw new AppError('未经认证的访问', 401);
      }
      
      // 检查用户是否具有所需角色
      if (!roles.includes(req.user.role)) {
        logger.warn(`用户 ${req.user.id} 尝试访问未授权的路由: ${req.originalUrl}`);
        throw new AppError('您没有权限执行此操作', 403);
      }
      
      logger.debug(`用户 ${req.user.id} 已授权访问 ${req.originalUrl}`);
      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  authenticateJWT,
  authorizeRoles
};