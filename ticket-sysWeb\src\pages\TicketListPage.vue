<template>
  <div class="p-6">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">票据管理</h1>
      <router-link
        to="/tickets/create"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        <Plus class="w-4 h-4 mr-2" />
        新建票据
      </router-link>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索框 -->
        <div class="md:col-span-2">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索票据编号、标题或描述..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              @input="handleSearch"
            />
          </div>
        </div>
        
        <!-- 状态筛选 -->
        <div>
          <select
            v-model="statusFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            @change="handleFilter"
          >
            <option value="">全部状态</option>
            <option value="pending">待处理</option>
            <option value="processing">处理中</option>
            <option value="completed">已完成</option>
            <option value="cancelled">已取消</option>
          </select>
        </div>
        
        <!-- 类型筛选 -->
        <div>
          <select
            v-model="typeFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            @change="handleFilter"
          >
            <option value="">全部类型</option>
            <option value="income">收入</option>
            <option value="expense">支出</option>
            <option value="transfer">转账</option>
            <option value="refund">退款</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 票据列表 -->
    <div class="bg-white rounded-lg shadow-sm border">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">共 {{ pagination.total }} 条记录</span>
            <div class="flex items-center space-x-2">
              <label class="text-sm text-gray-600">每页显示:</label>
              <select
                v-model="pagination.pageSize"
                class="px-2 py-1 border border-gray-300 rounded text-sm"
                @change="handlePageSizeChange"
              >
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
              </select>
            </div>
          </div>
          
          <div class="flex items-center space-x-2">
            <button
              @click="handleRefresh"
              class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              :disabled="loading"
            >
              <RefreshCw class="w-4 h-4" :class="{ 'animate-spin': loading }" />
            </button>
          </div>
        </div>
      </div>

      <!-- 表格内容 -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                票据信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                金额
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <!-- 加载状态 -->
            <tr v-if="loading">
              <td colspan="6" class="px-6 py-12 text-center">
                <div class="flex items-center justify-center">
                  <RefreshCw class="w-6 h-6 animate-spin text-blue-600 mr-2" />
                  <span class="text-gray-600">加载中...</span>
                </div>
              </td>
            </tr>
            
            <!-- 空状态 -->
            <tr v-else-if="tickets.length === 0">
              <td colspan="6" class="px-6 py-12 text-center">
                <div class="text-gray-500">
                  <FileText class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p class="text-lg font-medium mb-2">暂无票据</p>
                  <p class="text-sm">点击上方"新建票据"按钮创建第一个票据</p>
                </div>
              </td>
            </tr>
            
            <!-- 票据列表 -->
            <tr v-else v-for="ticket in tickets" :key="ticket.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ ticket.title }}</div>
                  <div class="text-sm text-gray-500">编号: {{ ticket.ticketNumber }}</div>
                  <div v-if="ticket.description" class="text-sm text-gray-500 mt-1 truncate max-w-xs">
                    {{ ticket.description }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <component :is="getTypeIcon(ticket.type)" class="w-4 h-4 mr-2" :class="getTypeColor(ticket.type)" />
                  <span class="text-sm text-gray-900">{{ getTypeLabel(ticket.type) }}</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm font-medium" :class="getAmountColor(ticket.type)">
                  {{ formatAmount(ticket.amount, ticket.type) }}
                </div>
              </td>
              <td class="px-6 py-4">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusColor(ticket.status)">
                  {{ getStatusLabel(ticket.status) }}
                </span>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500">
                {{ formatDate(ticket.createdAt) }}
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <router-link
                    :to="`/tickets/${ticket.id}`"
                    class="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    查看
                  </router-link>
                  <router-link
                    :to="`/tickets/${ticket.id}/edit`"
                    class="text-green-600 hover:text-green-800 text-sm"
                  >
                    编辑
                  </router-link>
                  <button
                    @click="handleDelete(ticket)"
                    class="text-red-600 hover:text-red-800 text-sm"
                  >
                    删除
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600">
            显示第 {{ (pagination.current - 1) * pagination.pageSize + 1 }} - 
            {{ Math.min(pagination.current * pagination.pageSize, pagination.total) }} 条，
            共 {{ pagination.total }} 条记录
          </div>
          
          <div class="flex items-center space-x-2">
            <button
              @click="handlePageChange(pagination.current - 1)"
              :disabled="pagination.current <= 1"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              上一页
            </button>
            
            <div class="flex items-center space-x-1">
              <button
                v-for="page in getPageNumbers()"
                :key="page"
                @click="handlePageChange(page)"
                class="px-3 py-1 border rounded text-sm"
                :class="{
                  'bg-blue-600 text-white border-blue-600': page === pagination.current,
                  'border-gray-300 hover:bg-gray-50': page !== pagination.current
                }"
              >
                {{ page }}
              </button>
            </div>
            
            <button
              @click="handlePageChange(pagination.current + 1)"
              :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Plus, 
  Search, 
  RefreshCw, 
  FileText,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft,
  RotateCcw
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import dayjs from 'dayjs'
import { useTicketStore } from '@/stores'
import { ticketApi } from '@/api'
import type { Ticket, PageQuery } from '@/types'
import { TicketStatus, TicketType } from '@/types'

const router = useRouter()
const ticketStore = useTicketStore()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const tickets = ref<Ticket[]>([])

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

/**
 * 获取票据列表
 */
const fetchTickets = async () => {
  try {
    loading.value = true
    
    const params: PageQuery = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      search: searchQuery.value || undefined,
      status: statusFilter.value || undefined,
      type: typeFilter.value || undefined
    }
    
    const response = await ticketApi.getTickets(params)
    tickets.value = response.list
    pagination.total = response.pagination.total
    pagination.current = response.pagination.current
    pagination.pageSize = response.pagination.pageSize
    
    // 更新 store
    ticketStore.setTickets(response.list)
  } catch (error) {
    console.error('获取票据列表失败:', error)
    toast.error('获取票据列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.current = 1
  fetchTickets()
}

/**
 * 处理筛选
 */
const handleFilter = () => {
  pagination.current = 1
  fetchTickets()
}

/**
 * 处理刷新
 */
const handleRefresh = () => {
  fetchTickets()
}

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchTickets()
}

/**
 * 处理每页大小变化
 */
const handlePageSizeChange = () => {
  pagination.current = 1
  fetchTickets()
}

/**
 * 处理删除票据
 */
const handleDelete = async (ticket: Ticket) => {
  if (!confirm(`确定要删除票据"${ticket.title}"吗？`)) {
    return
  }
  
  try {
    await ticketApi.deleteTicket(ticket.id)
    toast.success('删除成功')
    fetchTickets()
  } catch (error) {
    console.error('删除票据失败:', error)
    toast.error('删除失败')
  }
}

/**
 * 获取类型图标
 */
const getTypeIcon = (type: TicketType) => {
  const icons = {
    income: TrendingUp,
    expense: TrendingDown,
    transfer: ArrowRightLeft,
    refund: RotateCcw
  }
  return icons[type] || FileText
}

/**
 * 获取类型颜色
 */
const getTypeColor = (type: TicketType) => {
  const colors = {
    income: 'text-green-600',
    expense: 'text-red-600',
    transfer: 'text-blue-600',
    refund: 'text-orange-600'
  }
  return colors[type] || 'text-gray-600'
}

/**
 * 获取类型标签
 */
const getTypeLabel = (type: TicketType) => {
  const labels = {
    income: '收入',
    expense: '支出',
    transfer: '转账',
    refund: '退款'
  }
  return labels[type] || type
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status: TicketStatus) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  }
  return colors[status] || 'bg-gray-100 text-gray-800'
}

/**
 * 获取状态标签
 */
const getStatusLabel = (status: TicketStatus) => {
  const labels = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

/**
 * 获取金额颜色
 */
const getAmountColor = (type: TicketType) => {
  const colors = {
    income: 'text-green-600',
    expense: 'text-red-600',
    transfer: 'text-blue-600',
    refund: 'text-orange-600'
  }
  return colors[type] || 'text-gray-900'
}

/**
 * 格式化金额
 */
const formatAmount = (amount: number, type: TicketType) => {
  const prefix = type === TicketType.INCOME ? '+' : type === TicketType.EXPENSE ? '-' : ''
  return `${prefix}¥${amount.toLocaleString()}`
}

/**
 * 格式化日期
 */
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

/**
 * 获取页码数组
 */
const getPageNumbers = () => {
  const total = Math.ceil(pagination.total / pagination.pageSize)
  const current = pagination.current
  const pages: number[] = []
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push(-1) // 省略号
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push(-1) // 省略号
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1)
      pages.push(-1) // 省略号
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push(-1) // 省略号
      pages.push(total)
    }
  }
  
  return pages
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTickets()
})
</script>