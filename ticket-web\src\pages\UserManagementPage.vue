<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Key, 
  Eye,
  EyeOff,
  X,
  Check,
  User,
  Mail,
  Phone,
  Building,
  Briefcase,
  Calendar,
  Shield,
  UserCheck,
  UserX,
  RefreshCw
} from 'lucide-vue-next'
import { getUsers, createUser, updateUser, deleteUser, resetUserPassword } from '@/services/api'
import { useAuthStore } from '@/stores/useAuthStore'
import type { User as UserType, CreateUserRequest, UpdateUserRequest, UserQueryParams } from '@/types'

// 用户列表数据
const users = ref<UserType[]>([])
const total = ref(0)
const loading = ref(false)
const error = ref('')

// 查询参数
const queryParams = reactive<UserQueryParams>({
  page: 1,
  pageSize: 10,
  keyword: '',
  role: undefined,
  isActive: undefined,
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// 新建/编辑用户弹窗
const showUserModal = ref(false)
const isEditing = ref(false)
const editingUserId = ref('')

// 用户表单数据
const userForm = reactive<CreateUserRequest>({
  name: '',
  email: '',
  password: '',
  role: 'user',
  phone: '',
  department: '',
  position: ''
})

// 表单验证错误
const formErrors = reactive({
  name: '',
  email: '',
  password: '',
  phone: ''
})

// 密码可见性
const showPassword = ref(false)

// 删除确认弹窗
const showDeleteModal = ref(false)
const deletingUser = ref<UserType | null>(null)

// 重置密码弹窗
const showResetPasswordModal = ref(false)
const resetPasswordUserId = ref('')
const newPassword = ref('')

/**
 * 获取用户列表
 */
const fetchUsers = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const response = await getUsers(queryParams)
    console.log('API Response:', response) // 调试信息
    
    if (response.code === 200 && response.data?.data) {
      users.value = response.data.data.users || []
      total.value = response.data.data.total || 0
      console.log('Users loaded:', users.value.length) // 调试信息
    } else {
      error.value = response.message || '获取用户列表失败'
      console.error('API Error Response:', response)
    }
  } catch (err) {
    error.value = '网络错误，请检查连接'
    console.error('API Error:', err)
  } finally {
    loading.value = false
  }
}

/**
 * 搜索用户
 */
const handleSearch = () => {
  queryParams.page = 1
  fetchUsers()
}

/**
 * 重置搜索
 */
const resetSearch = () => {
  queryParams.keyword = ''
  queryParams.role = undefined
  queryParams.isActive = undefined
  queryParams.page = 1
  fetchUsers()
}

/**
 * 分页变化
 */
const handlePageChange = (page: number) => {
  queryParams.page = page
  fetchUsers()
}

/**
 * 打开新建用户弹窗
 */
const openCreateModal = () => {
  isEditing.value = false
  resetUserForm()
  showUserModal.value = true
}

/**
 * 打开编辑用户弹窗
 */
const openEditModal = (user: UserType) => {
  isEditing.value = true
  editingUserId.value = user.id
  
  userForm.name = user.name
  userForm.email = user.email
  userForm.password = '' // 编辑时不显示密码
  userForm.role = user.role
  userForm.phone = user.phone || ''
  userForm.department = user.department || ''
  userForm.position = user.position || ''
  
  showUserModal.value = true
}

/**
 * 重置用户表单
 */
const resetUserForm = () => {
  userForm.name = ''
  userForm.email = ''
  userForm.password = ''
  userForm.role = 'user'
  userForm.phone = ''
  userForm.department = ''
  userForm.position = ''
  
  // 清除错误
  Object.keys(formErrors).forEach(key => {
    formErrors[key as keyof typeof formErrors] = ''
  })
}

/**
 * 验证表单
 */
const validateForm = (): boolean => {
  let isValid = true
  
  // 重置错误
  Object.keys(formErrors).forEach(key => {
    formErrors[key as keyof typeof formErrors] = ''
  })
  
  // 验证姓名
  if (!userForm.name.trim()) {
    formErrors.name = '请输入姓名'
    isValid = false
  }
  
  // 验证邮箱
  if (!userForm.email.trim()) {
    formErrors.email = '请输入邮箱'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userForm.email)) {
    formErrors.email = '请输入有效的邮箱地址'
    isValid = false
  }
  
  // 验证密码（仅新建时）
  if (!isEditing.value) {
    if (!userForm.password.trim()) {
      formErrors.password = '请输入密码'
      isValid = false
    } else if (userForm.password.length < 6) {
      formErrors.password = '密码长度至少6位'
      isValid = false
    }
  }
  
  // 验证手机号（可选）
  if (userForm.phone && !/^1[3-9]\d{9}$/.test(userForm.phone)) {
    formErrors.phone = '请输入有效的手机号'
    isValid = false
  }
  
  return isValid
}

/**
 * 提交用户表单
 */
const submitUserForm = async () => {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    let response
    
    if (isEditing.value) {
      // 编辑用户
      const updateData: UpdateUserRequest = {
        name: userForm.name,
        email: userForm.email,
        role: userForm.role,
        phone: userForm.phone || undefined,
        department: userForm.department || undefined,
        position: userForm.position || undefined
      }
      response = await updateUser(editingUserId.value, updateData)
    } else {
      // 新建用户
      response = await createUser(userForm)
    }
    
    if (response.code === 200 || response.code === 201) {
      showUserModal.value = false
      resetUserForm()
      await fetchUsers()
      // 显示成功消息
      console.log(isEditing.value ? '用户更新成功' : '用户创建成功')
    } else {
      error.value = response.message || (isEditing.value ? '更新用户失败' : '创建用户失败')
      console.error('API Error Response:', response)
    }
  } catch (err) {
    error.value = isEditing.value ? '更新用户失败' : '创建用户失败'
    console.error('Submit Error:', err)
  } finally {
    loading.value = false
  }
}

/**
 * 打开删除确认弹窗
 */
const openDeleteModal = (user: UserType) => {
  deletingUser.value = user
  showDeleteModal.value = true
}

/**
 * 确认删除用户
 */
const confirmDelete = async () => {
  if (!deletingUser.value) return
  
  loading.value = true
  
  try {
    const response = await deleteUser(deletingUser.value.id)
    if (response.code === 200) {
      showDeleteModal.value = false
      fetchUsers()
    } else {
      error.value = response.message
    }
  } catch (err) {
    error.value = '删除用户失败'
    console.error(err)
  } finally {
    loading.value = false
  }
}

/**
 * 打开重置密码弹窗
 */
const openResetPasswordModal = (user: UserType) => {
  resetPasswordUserId.value = user.id
  newPassword.value = ''
  showResetPasswordModal.value = true
}

/**
 * 重置密码
 */
const confirmResetPassword = async () => {
  if (!newPassword.value || newPassword.value.length < 6) {
    error.value = '密码长度至少6位'
    return
  }
  
  loading.value = true
  
  try {
    const response = await resetUserPassword(resetPasswordUserId.value, newPassword.value)
    if (response.code === 200) {
      showResetPasswordModal.value = false
      newPassword.value = ''
    } else {
      error.value = response.message
    }
  } catch (err) {
    error.value = '重置密码失败'
    console.error(err)
  } finally {
    loading.value = false
  }
}

/**
 * 切换用户状态
 */
const toggleUserStatus = async (user: UserType) => {
  loading.value = true
  
  try {
    const response = await updateUser(user.id, { isActive: !user.isActive })
    if (response.code === 200) {
      fetchUsers()
    } else {
      error.value = response.message
    }
  } catch (err) {
    error.value = '更新用户状态失败'
    console.error(err)
  } finally {
    loading.value = false
  }
}

/**
 * 格式化日期
 */
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

/**
 * 获取角色显示文本
 */
const getRoleText = (role: string) => {
  return role === 'enterprise' ? '企业用户' : '普通用户'
}

/**
 * 获取状态显示文本
 */
const getStatusText = (isActive?: boolean) => {
  return isActive ? '正常' : '禁用'
}

/**
 * 测试API连接
 */
const testApiConnection = async () => {
  console.log('Testing API connection...')
  try {
    const response = await getUsers({ page: 1, pageSize: 5 })
    console.log('Test API Response:', response)
    if (response.code === 200) {
      console.log('API连接正常，用户数量:', response.data.data.users.length)
    } else {
      console.error('API返回错误:', response.message)
    }
  } catch (err) {
    console.error('API连接测试失败:', err)
  }
}

// 组件挂载时获取用户列表
onMounted(async () => {
  console.log('UserManagementPage mounted') // 调试信息
  
  // 检查用户权限
  const authStore = useAuthStore()
  console.log('Current user:', authStore.user) // 调试信息
  
  if (!authStore.isEnterprise) {
    error.value = '只有企业用户可以访问用户管理功能'
    return
  }
  
  await fetchUsers()
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
        <p class="text-gray-600 mt-1">管理系统用户账户和权限</p>
      </div>
      <div class="flex space-x-3">
        <button
          @click="testApiConnection"
          class="flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
        >
          <RefreshCw class="w-4 h-4 mr-2" />
          测试API
        </button>
        <button
          @click="openCreateModal"
          class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus class="w-4 h-4 mr-2" />
          新建用户
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 关键词搜索 -->
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search class="h-4 w-4 text-gray-400" />
          </div>
          <input
            v-model="queryParams.keyword"
            type="text"
            placeholder="搜索用户名、邮箱..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            @keyup.enter="handleSearch"
          />
        </div>

        <!-- 角色筛选 -->
        <select
          v-model="queryParams.role"
          class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">全部角色</option>
          <option value="user">普通用户</option>
          <option value="enterprise">企业用户</option>
        </select>

        <!-- 状态筛选 -->
        <select
          v-model="queryParams.isActive"
          class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">全部状态</option>
          <option :value="true">正常</option>
          <option :value="false">禁用</option>
        </select>

        <!-- 搜索按钮 -->
        <div class="flex space-x-2">
          <button
            @click="handleSearch"
            class="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Search class="w-4 h-4 mr-2" />
            搜索
          </button>
          <button
            @click="resetSearch"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            <RefreshCw class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">
            共 {{ total }} 个用户
          </h2>
          <button
            @click="fetchUsers"
            :disabled="loading"
            class="flex items-center px-3 py-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            <RefreshCw :class="['w-4 h-4 mr-1', loading ? 'animate-spin' : '']" />
            刷新
          </button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading && users.length === 0" class="flex items-center justify-center py-12">
        <div class="text-center">
          <RefreshCw class="w-8 h-8 text-gray-400 animate-spin mx-auto mb-2" />
          <p class="text-gray-500">加载中...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-red-600 mb-2">{{ error }}</p>
          <div class="text-xs text-gray-500 mb-4">
            <p>调试信息：请打开浏览器控制台查看详细错误</p>
            <p>当前查询参数：{{ JSON.stringify(queryParams) }}</p>
          </div>
          <div class="flex space-x-2 justify-center">
            <button
              @click="fetchUsers"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              重试
            </button>
            <button
              @click="testApiConnection"
              class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              测试API
            </button>
          </div>
        </div>
      </div>

      <!-- 用户表格 -->
      <div v-else-if="users.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                角色
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                最近登录
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in users" :key="user.id" class="hover:bg-gray-50">
              <!-- 用户信息 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <img 
                      v-if="user.avatar" 
                      :src="user.avatar" 
                      :alt="user.name"
                      class="h-10 w-10 rounded-full object-cover"
                    />
                    <div v-else class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                      <User class="h-5 w-5 text-gray-600" />
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                    <div class="text-sm text-gray-500">{{ user.email }}</div>
                    <div v-if="user.phone" class="text-xs text-gray-400">{{ user.phone }}</div>
                  </div>
                </div>
              </td>

              <!-- 角色 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  user.role === 'enterprise' 
                    ? 'bg-purple-100 text-purple-800' 
                    : 'bg-blue-100 text-blue-800'
                ]">
                  <Shield v-if="user.role === 'enterprise'" class="w-3 h-3 mr-1" />
                  <User v-else class="w-3 h-3 mr-1" />
                  {{ getRoleText(user.role) }}
                </span>
              </td>

              <!-- 状态 -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  user.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                ]">
                  <UserCheck v-if="user.isActive" class="w-3 h-3 mr-1" />
                  <UserX v-else class="w-3 h-3 mr-1" />
                  {{ getStatusText(user.isActive) }}
                </span>
              </td>

              <!-- 最近登录 -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(user.lastLoginAt) }}
              </td>

              <!-- 创建时间 -->
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(user.createdAt) }}
              </td>

              <!-- 操作 -->
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button
                    @click="openEditModal(user)"
                    class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors"
                    title="编辑"
                  >
                    <Edit class="w-4 h-4" />
                  </button>
                  
                  <button
                    @click="openResetPasswordModal(user)"
                    class="text-yellow-600 hover:text-yellow-900 p-1 rounded hover:bg-yellow-50 transition-colors"
                    title="重置密码"
                  >
                    <Key class="w-4 h-4" />
                  </button>
                  
                  <button
                    @click="toggleUserStatus(user)"
                    :class="[
                      'p-1 rounded transition-colors',
                      user.isActive 
                        ? 'text-red-600 hover:text-red-900 hover:bg-red-50' 
                        : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                    ]"
                    :title="user.isActive ? '禁用' : '启用'"
                  >
                    <UserX v-if="user.isActive" class="w-4 h-4" />
                    <UserCheck v-else class="w-4 h-4" />
                  </button>
                  
                  <button
                    @click="openDeleteModal(user)"
                    class="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors"
                    title="删除"
                    :disabled="user.email === '<EMAIL>'"
                  >
                    <Trash2 class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <User class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-500 mb-4">暂无用户数据</p>
          <button
            @click="openCreateModal"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            创建第一个用户
          </button>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > queryParams.pageSize" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示第 {{ (queryParams.page - 1) * queryParams.pageSize + 1 }} - 
            {{ Math.min(queryParams.page * queryParams.pageSize, total) }} 条，
            共 {{ total }} 条记录
          </div>
          <div class="flex space-x-2">
            <button
              @click="handlePageChange(queryParams.page - 1)"
              :disabled="queryParams.page <= 1"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              上一页
            </button>
            <button
              @click="handlePageChange(queryParams.page + 1)"
              :disabled="queryParams.page * queryParams.pageSize >= total"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建/编辑用户弹窗 -->
    <div v-if="showUserModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showUserModal = false"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form @submit.prevent="submitUserForm">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">
                  {{ isEditing ? '编辑用户' : '新建用户' }}
                </h3>
                <button
                  type="button"
                  @click="showUserModal = false"
                  class="text-gray-400 hover:text-gray-600"
                >
                  <X class="w-5 h-5" />
                </button>
              </div>

              <div class="space-y-4">
                <!-- 姓名 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">姓名 *</label>
                  <input
                    v-model="userForm.name"
                    type="text"
                    :class="[
                      'block w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500',
                      formErrors.name ? 'border-red-300' : 'border-gray-300'
                    ]"
                    placeholder="请输入姓名"
                  />
                  <p v-if="formErrors.name" class="mt-1 text-sm text-red-600">{{ formErrors.name }}</p>
                </div>

                <!-- 邮箱 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">邮箱 *</label>
                  <input
                    v-model="userForm.email"
                    type="email"
                    :class="[
                      'block w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500',
                      formErrors.email ? 'border-red-300' : 'border-gray-300'
                    ]"
                    placeholder="请输入邮箱地址"
                  />
                  <p v-if="formErrors.email" class="mt-1 text-sm text-red-600">{{ formErrors.email }}</p>
                </div>

                <!-- 密码 (仅新建时显示) -->
                <div v-if="!isEditing">
                  <label class="block text-sm font-medium text-gray-700 mb-1">密码 *</label>
                  <div class="relative">
                    <input
                      v-model="userForm.password"
                      :type="showPassword ? 'text' : 'password'"
                      :class="[
                        'block w-full px-3 py-2 pr-10 border rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500',
                        formErrors.password ? 'border-red-300' : 'border-gray-300'
                      ]"
                      placeholder="请输入密码"
                    />
                    <button
                      type="button"
                      @click="showPassword = !showPassword"
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <Eye v-if="!showPassword" class="h-4 w-4 text-gray-400" />
                      <EyeOff v-else class="h-4 w-4 text-gray-400" />
                    </button>
                  </div>
                  <p v-if="formErrors.password" class="mt-1 text-sm text-red-600">{{ formErrors.password }}</p>
                </div>

                <!-- 角色 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">角色 *</label>
                  <select
                    v-model="userForm.role"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="user">普通用户</option>
                    <option value="enterprise">企业用户</option>
                  </select>
                </div>

                <!-- 手机号 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                  <input
                    v-model="userForm.phone"
                    type="tel"
                    :class="[
                      'block w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500',
                      formErrors.phone ? 'border-red-300' : 'border-gray-300'
                    ]"
                    placeholder="请输入手机号"
                  />
                  <p v-if="formErrors.phone" class="mt-1 text-sm text-red-600">{{ formErrors.phone }}</p>
                </div>

                <!-- 部门 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                  <input
                    v-model="userForm.department"
                    type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入部门"
                  />
                </div>

                <!-- 职位 -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">职位</label>
                  <input
                    v-model="userForm.position"
                    type="text"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入职位"
                  />
                </div>
              </div>
            </div>

            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                :disabled="loading"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
              >
                {{ isEditing ? '更新' : '创建' }}
              </button>
              <button
                type="button"
                @click="showUserModal = false"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showDeleteModal = false"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <Trash2 class="h-6 w-6 text-red-600" />
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900">删除用户</h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    确定要删除用户 <strong>{{ deletingUser?.name }}</strong> 吗？此操作不可撤销。
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              @click="confirmDelete"
              :disabled="loading"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              删除
            </button>
            <button
              @click="showDeleteModal = false"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 重置密码弹窗 -->
    <div v-if="showResetPasswordModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showResetPasswordModal = false"></div>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form @submit.prevent="confirmResetPassword">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">重置密码</h3>
                <button
                  type="button"
                  @click="showResetPasswordModal = false"
                  class="text-gray-400 hover:text-gray-600"
                >
                  <X class="w-5 h-5" />
                </button>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                <input
                  v-model="newPassword"
                  type="password"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入新密码（至少6位）"
                />
              </div>
            </div>

            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                :disabled="loading"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
              >
                确认重置
              </button>
              <button
                type="button"
                @click="showResetPasswordModal = false"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 表格样式优化 */
table {
  border-collapse: collapse;
}

/* 悬停效果 */
tr:hover {
  background-color: rgba(249, 250, 251, 0.5);
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* 动画效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
</style>
