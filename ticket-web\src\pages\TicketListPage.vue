<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useTicketStore } from '@/stores/useTicketStore'
import { TicketType, TicketStatus, type TicketQueryParams } from '@/types'
import { 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Download, 
  Grid, 
  List,
  CheckSquare,
  Square,
  MoreHorizontal,
  Calendar,
  DollarSign
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const router = useRouter()
const ticketStore = useTicketStore()

// 视图模式
const viewMode = ref<'table' | 'card'>('table')

// 搜索和筛选参数
const searchParams = reactive<TicketQueryParams>({
  page: 1,
  pageSize: 10,
  keyword: '',
  type: undefined,
  status: undefined,
  startDate: '',
  endDate: '',
  minAmount: undefined,
  maxAmount: undefined,
  category: ''
})

// 筛选面板显示状态
const showFilters = ref(false)

// 票据类型选项
const ticketTypes = [
  { value: '', label: '全部类型' },
  { value: TicketType.INVOICE, label: '发票' },
  { value: TicketType.RECEIPT, label: '收据' },
  { value: TicketType.CHECK, label: '支票' },
  { value: TicketType.OTHER, label: '其他' }
]

// 票据状态选项
const ticketStatuses = [
  { value: '', label: '全部状态' },
  { value: TicketStatus.PENDING, label: '待处理' },
  { value: TicketStatus.VERIFIED, label: '已验证' },
  { value: TicketStatus.REJECTED, label: '已拒绝' }
]

/**
 * 组件挂载时获取数据
 */
onMounted(() => {
  fetchTickets()
})

/**
 * 获取票据列表
 */
const fetchTickets = async () => {
  await ticketStore.fetchTickets(searchParams)
}

/**
 * 搜索票据
 */
const searchTickets = () => {
  searchParams.page = 1
  fetchTickets()
}

/**
 * 重置筛选条件
 */
const resetFilters = () => {
  Object.assign(searchParams, {
    page: 1,
    pageSize: 10,
    keyword: '',
    type: undefined,
    status: undefined,
    startDate: '',
    endDate: '',
    minAmount: undefined,
    maxAmount: undefined,
    category: ''
  })
  fetchTickets()
}

/**
 * 切换视图模式
 * @param mode 视图模式
 */
const toggleViewMode = (mode: 'table' | 'card') => {
  viewMode.value = mode
}

/**
 * 全选/取消全选
 */
const toggleSelectAll = () => {
  if (ticketStore.selectedTickets.length === ticketStore.tickets.length) {
    ticketStore.clearSelection()
  } else {
    ticketStore.selectAllTickets()
  }
}

/**
 * 批量删除票据
 */
const batchDelete = async () => {
  if (ticketStore.selectedTickets.length === 0) {
    toast.error('请选择要删除的票据')
    return
  }
  
  if (confirm(`确定要删除选中的 ${ticketStore.selectedTickets.length} 个票据吗？`)) {
    try {
      await ticketStore.deleteTickets(ticketStore.selectedTickets)
      toast.success('批量删除成功')
      fetchTickets()
    } catch (error) {
      toast.error('批量删除失败')
    }
  }
}

/**
 * 批量导出票据
 */
const batchExport = () => {
  if (ticketStore.selectedTickets.length === 0) {
    toast.error('请选择要导出的票据')
    return
  }
  
  // 模拟导出功能
  toast.success(`正在导出 ${ticketStore.selectedTickets.length} 个票据...`)
}

/**
 * 删除单个票据
 * @param id 票据ID
 */
const deleteTicket = async (id: string) => {
  if (confirm('确定要删除这个票据吗？')) {
    try {
      await ticketStore.deleteTicket(id)
      toast.success('删除成功')
      fetchTickets()
    } catch (error) {
      toast.error('删除失败')
    }
  }
}

/**
 * 导航到票据详情
 * @param id 票据ID
 */
const viewTicket = (id: string) => {
  router.push(`/tickets/${id}`)
}

/**
 * 导航到编辑票据
 * @param id 票据ID
 */
const editTicket = (id: string) => {
  router.push(`/tickets/${id}/edit`)
}

/**
 * 格式化金额显示
 * @param amount 金额
 */
const formatAmount = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

/**
 * 格式化日期显示
 * @param date 日期字符串
 */
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

/**
 * 获取票据类型文本
 * @param type 类型
 */
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    invoice: '发票',
    receipt: '收据',
    check: '支票',
    other: '其他'
  }
  return typeMap[type] || '未知'
}

/**
 * 获取票据状态样式
 * @param status 状态
 */
const getStatusClass = (status: string) => {
  switch (status) {
    case 'verified':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

/**
 * 获取票据状态文本
 * @param status 状态
 */
const getStatusText = (status: string) => {
  switch (status) {
    case 'verified':
      return '已验证'
    case 'pending':
      return '待处理'
    case 'rejected':
      return '已拒绝'
    default:
      return '未知'
  }
}

// 计算属性
const isAllSelected = computed(() => {
  return ticketStore.tickets.length > 0 && 
         ticketStore.selectedTickets.length === ticketStore.tickets.length
})

const isIndeterminate = computed(() => {
  return ticketStore.selectedTickets.length > 0 && 
         ticketStore.selectedTickets.length < ticketStore.tickets.length
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">票据列表</h1>
        <p class="text-gray-600">管理和查看所有票据信息</p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button 
          @click="router.push('/create')"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Plus class="w-4 h-4 mr-2" />
          录入票据
        </button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <!-- 搜索栏 -->
      <div class="flex flex-col sm:flex-row gap-4 mb-4">
        <div class="flex-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search class="h-4 w-4 text-gray-400" />
            </div>
            <input 
              v-model="searchParams.keyword"
              type="text" 
              placeholder="搜索票据标题、描述或分类..."
              @keyup.enter="searchTickets"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
          </div>
        </div>
        <div class="flex space-x-2">
          <button 
            @click="searchTickets"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            搜索
          </button>
          <button 
            @click="showFilters = !showFilters"
            :class="[
              'px-4 py-2 border rounded-md transition-colors',
              showFilters 
                ? 'bg-blue-50 border-blue-300 text-blue-700' 
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            ]"
          >
            <Filter class="w-4 h-4 mr-2 inline" />
            筛选
          </button>
        </div>
      </div>
      
      <!-- 筛选面板 -->
      <div v-if="showFilters" class="border-t border-gray-200 pt-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- 票据类型 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">票据类型</label>
            <select 
              v-model="searchParams.type"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option v-for="type in ticketTypes" :key="type.value" :value="type.value">
                {{ type.label }}
              </option>
            </select>
          </div>
          
          <!-- 状态 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
            <select 
              v-model="searchParams.status"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option v-for="status in ticketStatuses" :key="status.value" :value="status.value">
                {{ status.label }}
              </option>
            </select>
          </div>
          
          <!-- 开始日期 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
            <input 
              v-model="searchParams.startDate"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
          </div>
          
          <!-- 结束日期 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
            <input 
              v-model="searchParams.endDate"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
          </div>
        </div>
        
        <div class="flex justify-end space-x-2 mt-4">
          <button 
            @click="resetFilters"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            重置
          </button>
          <button 
            @click="fetchTickets"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            应用筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <!-- 左侧：批量操作 -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <button 
              @click="toggleSelectAll"
              class="p-1 rounded hover:bg-gray-100"
            >
              <CheckSquare v-if="isAllSelected" class="w-5 h-5 text-blue-600" />
              <Square v-else-if="!isIndeterminate" class="w-5 h-5 text-gray-400" />
              <div v-else class="w-5 h-5 bg-blue-600 rounded border-2 border-blue-600 relative">
                <div class="absolute inset-1 bg-white rounded-sm"></div>
              </div>
            </button>
            <span class="ml-2 text-sm text-gray-600">
              已选择 {{ ticketStore.selectedTickets.length }} 项
            </span>
          </div>
          
          <div v-if="ticketStore.selectedTickets.length > 0" class="flex items-center space-x-2">
            <button 
              @click="batchDelete"
              class="inline-flex items-center px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
            >
              <Trash2 class="w-4 h-4 mr-1" />
              删除
            </button>
            <button 
              @click="batchExport"
              class="inline-flex items-center px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
            >
              <Download class="w-4 h-4 mr-1" />
              导出
            </button>
          </div>
        </div>
        
        <!-- 右侧：视图切换 -->
        <div class="mt-4 sm:mt-0 flex items-center space-x-2">
          <span class="text-sm text-gray-600">视图：</span>
          <div class="flex border border-gray-300 rounded-md">
            <button 
              @click="toggleViewMode('table')"
              :class="[
                'px-3 py-1 text-sm transition-colors',
                viewMode === 'table' 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-600 hover:bg-gray-100'
              ]"
            >
              <List class="w-4 h-4" />
            </button>
            <button 
              @click="toggleViewMode('card')"
              :class="[
                'px-3 py-1 text-sm transition-colors border-l border-gray-300',
                viewMode === 'card' 
                  ? 'bg-blue-600 text-white' 
                  : 'text-gray-600 hover:bg-gray-100'
              ]"
            >
              <Grid class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 票据列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input 
                  type="checkbox" 
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  @change="toggleSelectAll"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                票据信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                金额
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                日期
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr 
              v-for="ticket in ticketStore.tickets" 
              :key="ticket.id"
              class="hover:bg-gray-50"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <input 
                  type="checkbox" 
                  :checked="ticketStore.selectedTickets.includes(ticket.id)"
                  @change="ticketStore.toggleTicketSelection(ticket.id)"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ ticket.title }}</div>
                    <div class="text-sm text-gray-500">{{ ticket.category || '未分类' }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm text-gray-900">{{ getTypeText(ticket.type) }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm font-medium text-gray-900">{{ formatAmount(ticket.amount) }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm text-gray-900">{{ formatDate(ticket.date) }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    getStatusClass(ticket.status)
                  ]"
                >
                  {{ getStatusText(ticket.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button 
                    @click="viewTicket(ticket.id)"
                    class="text-blue-600 hover:text-blue-900"
                    title="查看详情"
                  >
                    <Eye class="w-4 h-4" />
                  </button>
                  <button 
                    @click="editTicket(ticket.id)"
                    class="text-green-600 hover:text-green-900"
                    title="编辑"
                  >
                    <Edit class="w-4 h-4" />
                  </button>
                  <button 
                    @click="deleteTicket(ticket.id)"
                    class="text-red-600 hover:text-red-900"
                    title="删除"
                  >
                    <Trash2 class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 卡片视图 -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
        <div 
          v-for="ticket in ticketStore.tickets" 
          :key="ticket.id"
          class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-center">
              <input 
                type="checkbox" 
                :checked="ticketStore.selectedTickets.includes(ticket.id)"
                @change="ticketStore.toggleTicketSelection(ticket.id)"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-3"
              >
              <span 
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  getStatusClass(ticket.status)
                ]"
              >
                {{ getStatusText(ticket.status) }}
              </span>
            </div>
            <div class="relative">
              <button class="p-1 rounded hover:bg-gray-100">
                <MoreHorizontal class="w-4 h-4 text-gray-400" />
              </button>
            </div>
          </div>
          
          <h3 class="text-lg font-medium text-gray-900 mb-2">{{ ticket.title }}</h3>
          
          <div class="space-y-2 text-sm text-gray-600">
            <div class="flex items-center">
              <span class="font-medium">类型：</span>
              <span class="ml-1">{{ getTypeText(ticket.type) }}</span>
            </div>
            <div class="flex items-center">
              <DollarSign class="w-4 h-4 mr-1" />
              <span class="font-medium text-gray-900">{{ formatAmount(ticket.amount) }}</span>
            </div>
            <div class="flex items-center">
              <Calendar class="w-4 h-4 mr-1" />
              <span>{{ formatDate(ticket.date) }}</span>
            </div>
            <div v-if="ticket.category">
              <span class="font-medium">分类：</span>
              <span class="ml-1">{{ ticket.category }}</span>
            </div>
          </div>
          
          <div class="flex justify-end space-x-2 mt-4 pt-4 border-t border-gray-200">
            <button 
              @click="viewTicket(ticket.id)"
              class="p-2 text-blue-600 hover:bg-blue-50 rounded transition-colors"
              title="查看详情"
            >
              <Eye class="w-4 h-4" />
            </button>
            <button 
              @click="editTicket(ticket.id)"
              class="p-2 text-green-600 hover:bg-green-50 rounded transition-colors"
              title="编辑"
            >
              <Edit class="w-4 h-4" />
            </button>
            <button 
              @click="deleteTicket(ticket.id)"
              class="p-2 text-red-600 hover:bg-red-50 rounded transition-colors"
              title="删除"
            >
              <Trash2 class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="ticketStore.tickets.length === 0" class="text-center py-12">
        <div class="text-gray-400 mb-4">
          <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无票据数据</h3>
        <p class="text-gray-600 mb-4">开始录入您的第一个票据吧</p>
        <button 
          @click="router.push('/create')"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Plus class="w-4 h-4 mr-2" />
          录入票据
        </button>
      </div>
    </div>
  </div>
</template>