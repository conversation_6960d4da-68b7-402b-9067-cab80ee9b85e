/**
 * 数据库连接配置
 * 用于建立与MySQL数据库的连接
 */
const mysql = require('mysql2/promise');
require('dotenv').config();

// 从环境变量中获取数据库连接信息
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'rootroot',
  database: process.env.DB_NAME || 'ticket',
};

/**
 * 创建数据库连接池
 * @returns {Promise<mysql.Pool>} 返回数据库连接池
 */
const createPool = async () => {
  try {
    const pool = mysql.createPool(dbConfig);
    console.log('数据库连接池创建成功');
    return pool;
  } catch (error) {
    console.error('数据库连接池创建失败:', error.message);
    throw error;
  }
};

/**
 * 测试数据库连接并获取所有表名
 * @returns {Promise<Array>} 返回数据库中的所有表名
 */
const testConnection = async () => {
  try {
    const pool = await createPool();
    const connection = await pool.getConnection();
    console.log('数据库连接成功!');
    
    // 查询所有表名
    const [tables] = await connection.query(
      `SELECT table_name FROM information_schema.tables WHERE table_schema = '${dbConfig.database}'`
    );
    
    console.log('数据库中的表:');
    tables.forEach(table => {
      console.log(`- ${table.table_name}`);
    });
    
    connection.release();
    return tables.map(table => table.table_name);
  } catch (error) {
    console.error('数据库连接测试失败:', error.message);
    throw error;
  }
};

module.exports = {
  createPool,
  testConnection,
  dbConfig
};