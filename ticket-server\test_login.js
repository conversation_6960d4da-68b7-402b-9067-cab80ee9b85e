/**
 * 测试登录并获取token
 */
const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api';

async function testLogin() {
  try {
    console.log('测试管理员登录...');
    const response = await axios.post(`${BASE_URL}/admin/login`, {
      username: 'admin',
      password: 'admin123456'
    });
    
    console.log('登录响应:', JSON.stringify(response.data, null, 2));
    
    if (response.data.status === 'success') {
      const token = response.data.data.token;
      console.log('\n获取到的token:', token);
      
      // 测试用户列表接口
      console.log('\n测试管理员用户列表接口...');
      const usersResponse = await axios.get(`${BASE_URL}/admin/users`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('用户列表响应:', JSON.stringify(usersResponse.data, null, 2));
    }
  } catch (error) {
    console.error('错误:', error.response?.data || error.message);
  }
}

testLogin();
