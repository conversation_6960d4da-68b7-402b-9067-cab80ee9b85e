<template>
  <div class="p-6">
    <!-- 页面标题和操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
      <button
        @click="showCreateModal = true"
        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        <UserPlus class="w-4 h-4 mr-2" />
        新建用户
      </button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- 搜索框 -->
        <div class="md:col-span-2">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索用户名、邮箱..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              @input="handleSearch"
            />
          </div>
        </div>
        
        <!-- 角色筛选 -->
        <div>
          <select
            v-model="roleFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            @change="handleFilter"
          >
            <option value="">全部角色</option>
            <option value="admin">管理员</option>
            <option value="user">普通用户</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white rounded-lg shadow-sm border">
      <!-- 表格头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">共 {{ pagination.total }} 个用户</span>
          </div>
          
          <div class="flex items-center space-x-2">
            <button
              @click="handleRefresh"
              class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              :disabled="loading"
            >
              <RefreshCw class="w-4 h-4" :class="{ 'animate-spin': loading }" />
            </button>
          </div>
        </div>
      </div>

      <!-- 表格内容 -->
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                角色
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                最后登录
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <!-- 加载状态 -->
            <tr v-if="loading">
              <td colspan="6" class="px-6 py-12 text-center">
                <div class="flex items-center justify-center">
                  <RefreshCw class="w-6 h-6 animate-spin text-blue-600 mr-2" />
                  <span class="text-gray-600">加载中...</span>
                </div>
              </td>
            </tr>
            
            <!-- 空状态 -->
            <tr v-else-if="users.length === 0">
              <td colspan="6" class="px-6 py-12 text-center">
                <div class="text-gray-500">
                  <Users class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p class="text-lg font-medium mb-2">暂无用户</p>
                  <p class="text-sm">点击上方"新建用户"按钮创建第一个用户</p>
                </div>
              </td>
            </tr>
            
            <!-- 用户列表 -->
            <tr v-else v-for="user in users" :key="user.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <User class="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                    <div class="text-sm text-gray-500">{{ user.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="getRoleColor(user.role)">
                  {{ getRoleLabel(user.role) }}
                </span>
              </td>
              <td class="px-6 py-4">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusColor(user.status)">
                  {{ getStatusLabel(user.status) }}
                </span>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500">
                {{ user.lastLoginAt ? dayjs(user.lastLoginAt).format('YYYY-MM-DD HH:mm') : '从未登录' }}
              </td>
              <td class="px-6 py-4 text-sm text-gray-500">
                {{ dayjs(user.createdAt).format('YYYY-MM-DD HH:mm') }}
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <button
                    @click="editUser(user)"
                    class="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    编辑
                  </button>
                  <button
                    @click="toggleUserStatus(user)"
                    class="text-sm"
                    :class="user.status === 'active' ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'"
                  >
                    {{ user.status === 'active' ? '禁用' : '启用' }}
                  </button>
                  <button
                    @click="deleteUser(user)"
                    class="text-red-600 hover:text-red-800 text-sm"
                  >
                    删除
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600">
            显示第 {{ (pagination.current - 1) * pagination.pageSize + 1 }} - 
            {{ Math.min(pagination.current * pagination.pageSize, pagination.total) }} 条，
            共 {{ pagination.total }} 条记录
          </div>
          
          <div class="flex items-center space-x-2">
            <button
              @click="handlePageChange(pagination.current - 1)"
              :disabled="pagination.current <= 1"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              上一页
            </button>
            
            <div class="flex items-center space-x-1">
              <button
                v-for="page in getPageNumbers()"
                :key="page"
                @click="handlePageChange(page)"
                class="px-3 py-1 border rounded text-sm"
                :class="{
                  'bg-blue-600 text-white border-blue-600': page === pagination.current,
                  'border-gray-300 hover:bg-gray-50': page !== pagination.current
                }"
              >
                {{ page }}
              </button>
            </div>
            
            <button
              @click="handlePageChange(pagination.current + 1)"
              :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑用户模态框 -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">
            {{ showCreateModal ? '新建用户' : '编辑用户' }}
          </h2>
        </div>
        
        <form @submit.prevent="handleSubmit" class="px-6 py-4 space-y-4">
          <!-- 用户名 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              用户名 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="userForm.username"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入用户名"
            />
          </div>
          
          <!-- 邮箱 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              邮箱 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="userForm.email"
              type="email"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入邮箱"
            />
          </div>
          
          <!-- 密码 -->
          <div v-if="showCreateModal">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              密码 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="userForm.password"
              type="password"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入密码"
            />
          </div>
          
          <!-- 角色 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">
              角色 <span class="text-red-500">*</span>
            </label>
            <select
              v-model="userForm.role"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="user">普通用户</option>
              <option value="admin">管理员</option>
            </select>
          </div>
        </form>
        
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="closeModal"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button
            @click="handleSubmit"
            :disabled="submitting"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {{ submitting ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import {
  UserPlus,
  Search,
  RefreshCw,
  Users,
  User
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import dayjs from 'dayjs'
import { userApi } from '@/api'
import type { User as UserType, PageQuery, UserCreateForm, UserUpdateForm } from '@/types'
import { UserRole } from '@/types'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const searchQuery = ref('')
const roleFilter = ref('')
const users = ref<UserType[]>([])
const showCreateModal = ref(false)
const showEditModal = ref(false)
const editingUser = ref<UserType | null>(null)

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 用户表单
const userForm = reactive<UserCreateForm>({
  username: '',
  email: '',
  password: '',
  role: UserRole.USER
})

/**
 * 获取用户列表
 */
const fetchUsers = async () => {
  try {
    loading.value = true
    
    const params: PageQuery = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      search: searchQuery.value || undefined,
      role: roleFilter.value || undefined
    }
    
    const response = await userApi.getUsers(params)
    users.value = response.list
    pagination.total = response.pagination.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
    toast.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.current = 1
  fetchUsers()
}

/**
 * 处理筛选
 */
const handleFilter = () => {
  pagination.current = 1
  fetchUsers()
}

/**
 * 处理刷新
 */
const handleRefresh = () => {
  fetchUsers()
}

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  pagination.current = page
  fetchUsers()
}

/**
 * 编辑用户
 */
const editUser = (user: UserType) => {
  editingUser.value = user
  userForm.username = user.username
  userForm.email = user.email
  userForm.role = user.role
  userForm.password = ''
  showEditModal.value = true
}

/**
 * 切换用户状态
 */
const toggleUserStatus = async (user: UserType) => {
  const newStatus = user.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'active' ? '启用' : '禁用'
  
  if (!confirm(`确定要${action}用户"${user.username}"吗？`)) {
    return
  }
  
  try {
    await userApi.updateUser(user.id, { status: newStatus })
    user.status = newStatus
    toast.success(`${action}成功`)
  } catch (error) {
    console.error(`${action}用户失败:`, error)
    toast.error(`${action}失败`)
  }
}

/**
 * 删除用户
 */
const deleteUser = async (user: UserType) => {
  if (!confirm(`确定要删除用户"${user.username}"吗？此操作不可恢复。`)) {
    return
  }
  
  try {
    await userApi.deleteUser(user.id)
    toast.success('删除成功')
    fetchUsers()
  } catch (error) {
    console.error('删除用户失败:', error)
    toast.error('删除失败')
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    if (showCreateModal.value) {
      // 创建用户
      await userApi.createUser({
        username: userForm.username,
        email: userForm.email,
        password: userForm.password,
        role: userForm.role
      })
      toast.success('用户创建成功')
    } else if (showEditModal.value && editingUser.value) {
      // 更新用户
      await userApi.updateUser(editingUser.value.id, {
        username: userForm.username,
        email: userForm.email,
        role: userForm.role
      })
      toast.success('用户更新成功')
    }
    
    closeModal()
    fetchUsers()
  } catch (error) {
    console.error('保存用户失败:', error)
    toast.error('保存失败')
  } finally {
    submitting.value = false
  }
}

/**
 * 关闭模态框
 */
const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingUser.value = null
  
  // 重置表单
  userForm.username = ''
  userForm.email = ''
  userForm.password = ''
  userForm.role = UserRole.USER
}

/**
 * 获取角色颜色
 */
const getRoleColor = (role: UserRole) => {
  const colors = {
    admin: 'bg-purple-100 text-purple-800',
    user: 'bg-blue-100 text-blue-800'
  }
  return colors[role] || 'bg-gray-100 text-gray-800'
}

/**
 * 获取角色标签
 */
const getRoleLabel = (role: UserRole) => {
  const labels = {
    admin: '管理员',
    user: '普通用户'
  }
  return labels[role] || role
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status: string) => {
  const colors = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-red-100 text-red-800'
  }
  return colors[status] || 'bg-gray-100 text-gray-800'
}

/**
 * 获取状态标签
 */
const getStatusLabel = (status: string) => {
  const labels = {
    active: '正常',
    inactive: '禁用'
  }
  return labels[status] || status
}

/**
 * 格式化日期
 */
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

/**
 * 获取页码数组
 */
const getPageNumbers = () => {
  const total = Math.ceil(pagination.total / pagination.pageSize)
  const current = pagination.current
  const pages: number[] = []
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push(-1) // 省略号
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push(-1) // 省略号
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1)
      pages.push(-1) // 省略号
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push(-1) // 省略号
      pages.push(total)
    }
  }
  
  return pages
}

// 组件挂载时获取数据
onMounted(() => {
  fetchUsers()
})
</script>