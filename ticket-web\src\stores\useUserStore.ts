import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { User, CreateUserRequest, UpdateUserRequest, UserQueryParams } from '@/types'
import { getUsers, createUser, updateUser, deleteUser, resetUserPassword } from '@/services/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const users = ref<User[]>([])
  const total = ref(0)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const activeUsers = computed(() => users.value.filter(user => user.isActive))
  const enterpriseUsers = computed(() => users.value.filter(user => user.role === 'enterprise'))
  const normalUsers = computed(() => users.value.filter(user => user.role === 'user'))

  /**
   * 获取用户列表
   */
  const fetchUsers = async (params: Partial<UserQueryParams> = {}) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await getUsers(params)
      
      if (response.code === 200 && response.data?.data) {
        users.value = response.data.data.users || []
        total.value = response.data.data.total || 0
        return { success: true, data: response.data.data }
      } else {
        error.value = response.message || '获取用户列表失败'
        return { success: false, message: error.value }
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('Fetch users error:', err)
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 创建用户
   */
  const addUser = async (userData: CreateUserRequest) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await createUser(userData)
      
      if (response.code === 201 && response.data?.data) {
        // 重新获取用户列表
        await fetchUsers()
        return { success: true, data: response.data.data }
      } else {
        error.value = response.message || '创建用户失败'
        return { success: false, message: error.value }
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('Create user error:', err)
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 更新用户
   */
  const editUser = async (id: string, userData: UpdateUserRequest) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await updateUser(id, userData)
      
      if (response.code === 200 && response.data?.data) {
        // 重新获取用户列表
        await fetchUsers()
        return { success: true, data: response.data.data }
      } else {
        error.value = response.message || '更新用户失败'
        return { success: false, message: error.value }
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('Update user error:', err)
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 删除用户
   */
  const removeUser = async (id: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await deleteUser(id)
      
      if (response.code === 200) {
        // 重新获取用户列表
        await fetchUsers()
        return { success: true }
      } else {
        error.value = response.message || '删除用户失败'
        return { success: false, message: error.value }
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('Delete user error:', err)
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 重置用户密码
   */
  const resetPassword = async (id: string, newPassword: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const response = await resetUserPassword(id, newPassword)
      
      if (response.code === 200) {
        return { success: true }
      } else {
        error.value = response.message || '重置密码失败'
        return { success: false, message: error.value }
      }
    } catch (err) {
      error.value = '网络错误，请稍后重试'
      console.error('Reset password error:', err)
      return { success: false, message: error.value }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 清除错误信息
   */
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    users,
    total,
    isLoading,
    error,
    
    // 计算属性
    activeUsers,
    enterpriseUsers,
    normalUsers,
    
    // 方法
    fetchUsers,
    addUser,
    editUser,
    removeUser,
    resetPassword,
    clearError
  }
})
