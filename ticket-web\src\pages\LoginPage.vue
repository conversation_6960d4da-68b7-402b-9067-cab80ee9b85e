<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/useAuthStore'
import { Eye, EyeOff, Mail, Lock, LogIn, FileText, CheckCircle, BarChart3 } from 'lucide-vue-next'

const router = useRouter()
const authStore = useAuthStore()

// 表单数据
const form = reactive({
  email: '',
  password: ''
})

// 表单验证错误
const formErrors = reactive({
  email: '',
  password: ''
})

// 密码可见性
const showPassword = ref(false)

// 记住我
const rememberMe = ref(false)

/**
 * 切换密码可见性
 */
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

/**
 * 验证邮箱格式
 */
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证表单
 */
const validateForm = (): boolean => {
  let isValid = true
  
  // 重置错误信息
  formErrors.email = ''
  formErrors.password = ''
  
  // 验证邮箱
  if (!form.email) {
    formErrors.email = '请输入邮箱地址'
    isValid = false
  } else if (!validateEmail(form.email)) {
    formErrors.email = '请输入有效的邮箱地址'
    isValid = false
  }
  
  // 验证密码
  if (!form.password) {
    formErrors.password = '请输入密码'
    isValid = false
  } else if (form.password.length < 6) {
    formErrors.password = '密码长度至少6位'
    isValid = false
  }
  
  return isValid
}

/**
 * 处理登录
 */
const handleLogin = async () => {
  // 清除之前的错误
  authStore.clearError()
  
  // 验证表单
  if (!validateForm()) {
    return
  }
  
  try {
    const result = await authStore.login({
      email: form.email,
      password: form.password
    })
    
    if (result.success) {
      // 如果选择记住我，保存邮箱到localStorage
      if (rememberMe.value) {
        localStorage.setItem('remembered_email', form.email)
      } else {
        localStorage.removeItem('remembered_email')
      }
      
      // 登录成功，跳转到重定向页面或首页
      const redirect = router.currentRoute.value.query.redirect as string
      router.push(redirect || '/')
    }
  } catch (err) {
    console.error('登录失败:', err)
  }
}

/**
 * 初始化表单（恢复记住的邮箱）
 */
const initForm = () => {
  const rememberedEmail = localStorage.getItem('remembered_email')
  if (rememberedEmail) {
    form.email = rememberedEmail
    rememberMe.value = true
  }
}

// 初始化表单
initForm()
</script>

<template>
  <div class="min-h-screen flex">
    <!-- 左侧产品介绍区域 -->
    <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-500 to-blue-700 relative overflow-hidden">
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20"></div>
      <div class="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full transform translate-x-1/2 -translate-y-1/2"></div>
      <div class="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full transform -translate-x-1/2 translate-y-1/2"></div>
      
      <!-- 内容 -->
      <div class="relative z-10 flex flex-col justify-center px-12 py-16 text-white">
        <div class="max-w-md fade-in-up">
          <h1 class="text-5xl font-bold mb-8 leading-tight">票据助手<br/>管理系统</h1>
          <p class="text-xl mb-10 text-blue-100 leading-relaxed">
            高效管理您的票据流程，简化财务工作，提升办公效率。安全、智能、便捷的票据解决方案。
          </p>
          
          <!-- 产品演示图片 -->
          <div class="bg-white/10 rounded-xl p-4 mb-8 backdrop-blur-sm border border-white/20">
            <div class="bg-gray-900 rounded-lg overflow-hidden shadow-2xl">
              <!-- 模拟浏览器标题栏 -->
              <div class="bg-gray-800 px-4 py-3 flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <div class="ml-4 text-xs text-gray-400">票据助手管理系统</div>
              </div>
              
              <!-- 模拟界面内容 -->
              <div class="bg-gray-100 p-4 aspect-video">
                <!-- 顶部导航栏 -->
                <div class="bg-white rounded-lg mb-3 p-3 shadow-sm">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <div class="w-6 h-6 bg-blue-500 rounded"></div>
                      <div class="text-xs text-gray-600">票据管理</div>
                    </div>
                    <div class="w-16 h-4 bg-gray-200 rounded"></div>
                  </div>
                </div>
                
                <!-- 主要内容区域 -->
                <div class="grid grid-cols-3 gap-3 h-32">
                  <!-- 左侧菜单 -->
                  <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="space-y-2">
                      <div class="w-full h-2 bg-blue-500 rounded"></div>
                      <div class="w-3/4 h-2 bg-gray-200 rounded"></div>
                      <div class="w-2/3 h-2 bg-gray-200 rounded"></div>
                      <div class="w-5/6 h-2 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                  
                  <!-- 中间内容 -->
                  <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="grid grid-cols-2 gap-2 h-full">
                      <div class="bg-blue-100 rounded p-2">
                        <div class="w-full h-2 bg-blue-400 rounded mb-1"></div>
                        <div class="w-2/3 h-1 bg-blue-300 rounded"></div>
                      </div>
                      <div class="bg-green-100 rounded p-2">
                        <div class="w-full h-2 bg-green-400 rounded mb-1"></div>
                        <div class="w-3/4 h-1 bg-green-300 rounded"></div>
                      </div>
                      <div class="bg-yellow-100 rounded p-2">
                        <div class="w-full h-2 bg-yellow-400 rounded mb-1"></div>
                        <div class="w-1/2 h-1 bg-yellow-300 rounded"></div>
                      </div>
                      <div class="bg-purple-100 rounded p-2">
                        <div class="w-full h-2 bg-purple-400 rounded mb-1"></div>
                        <div class="w-4/5 h-1 bg-purple-300 rounded"></div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 右侧统计 -->
                  <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="space-y-2">
                      <div class="w-full h-3 bg-gradient-to-r from-blue-400 to-blue-600 rounded"></div>
                      <div class="w-4/5 h-2 bg-gray-200 rounded"></div>
                      <div class="w-3/5 h-2 bg-gray-200 rounded"></div>
                      <div class="w-2/3 h-2 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 特性列表 -->
          <div class="space-y-6">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center mr-4 backdrop-blur-sm">
                <CheckCircle class="w-5 h-5 text-white" />
              </div>
              <span class="text-white font-medium">银行级加密技术，保障您的数据安全</span>
            </div>
            <div class="flex items-center">
              <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center mr-4 backdrop-blur-sm">
                <BarChart3 class="w-5 h-5 text-white" />
              </div>
              <span class="text-white font-medium">智能数据分析，洞察财务趋势</span>
            </div>
          </div>
          
          <!-- 用户数量 -->
          <div class="mt-12 flex items-center">
            <div class="flex -space-x-3 mr-4">
              <!-- 模拟用户头像 -->
              <div class="w-10 h-10 rounded-full border-3 border-white shadow-lg overflow-hidden bg-gradient-to-br from-pink-400 to-red-400 flex items-center justify-center">
                <span class="text-white text-xs font-bold">张</span>
              </div>
              <div class="w-10 h-10 rounded-full border-3 border-white shadow-lg overflow-hidden bg-gradient-to-br from-green-400 to-blue-400 flex items-center justify-center">
                <span class="text-white text-xs font-bold">李</span>
              </div>
              <div class="w-10 h-10 rounded-full border-3 border-white shadow-lg overflow-hidden bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center">
                <span class="text-white text-xs font-bold">王</span>
              </div>
              <div class="w-10 h-10 rounded-full border-3 border-white shadow-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                <span class="text-white text-sm font-bold">+</span>
              </div>
            </div>
            <div>
              <p class="text-white font-medium">已有超过1000+企业选择我们</p>
              <p class="text-blue-200 text-sm">值得信赖的票据管理解决方案</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧登录区域 -->
    <div class="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gradient-to-br from-gray-50 to-white">
      <div class="max-w-md w-full">
        <!-- 登录表单 -->
        <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-10 fade-in-up">
          <!-- 标题 -->
          <div class="text-center mb-10">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg float-animation">
              <LogIn class="w-8 h-8 text-white" />
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-3">欢迎回来</h2>
            <p class="text-gray-600 text-lg">请登录您的账号信息登录系统</p>
          </div>

        <!-- 登录表单 -->
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- 邮箱输入 -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
              用户名/邮箱
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Mail class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="email"
                v-model="form.email"
                type="email"
                autocomplete="email"
                :class="[
                  'block w-full pl-12 pr-4 py-4 border rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all',
                  formErrors.email ? 'border-red-300 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                ]"
                placeholder="请输入用户名或邮箱"
              />
            </div>
            <p v-if="formErrors.email" class="mt-2 text-sm text-red-600">
              {{ formErrors.email }}
            </p>
          </div>

          <!-- 密码输入 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
              密码
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Lock class="h-5 w-5 text-gray-400" />
              </div>
              <input
                id="password"
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                autocomplete="current-password"
                :class="[
                  'block w-full pl-12 pr-12 py-4 border rounded-lg text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all',
                  formErrors.password ? 'border-red-300 bg-red-50' : 'border-gray-200 hover:border-gray-300'
                ]"
                placeholder="请输入密码"
              />
              <button
                type="button"
                @click="togglePasswordVisibility"
                class="absolute inset-y-0 right-0 pr-4 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors"
              >
                <Eye v-if="!showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" />
                <EyeOff v-else class="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            </div>
            <p v-if="formErrors.password" class="mt-2 text-sm text-red-600">
              {{ formErrors.password }}
            </p>
          </div>

          <!-- 记住我和忘记密码 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="rememberMe"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                记住我
              </label>
            </div>
            <div class="text-sm">
              <a href="#" class="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                忘记密码？
              </a>
            </div>
          </div>

          <!-- 错误提示 -->
          <div v-if="authStore.error" class="bg-red-50 border border-red-200 rounded-lg p-4">
            <p class="text-sm text-red-800">{{ authStore.error }}</p>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            :disabled="authStore.isLoading"
            :class="[
              'w-full flex justify-center items-center py-4 px-6 border border-transparent rounded-lg shadow-sm text-base font-medium text-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
              authStore.isLoading 
                ? 'bg-gray-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg'
            ]"
          >
            <span v-if="authStore.isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              登录中...
            </span>
            <span v-else class="flex items-center">
              登录
              <LogIn class="ml-2 h-4 w-4" />
            </span>
          </button>
        </form>

        <!-- 测试账户提示 -->
        <div class="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <h3 class="text-sm font-medium text-gray-900 mb-3">测试账户</h3>
          <div class="text-xs text-gray-600 space-y-2">
            <div class="flex justify-between items-center">
              <span><strong>管理员:</strong> <EMAIL></span>
              <span class="text-blue-600">admin123</span>
            </div>
            <div class="flex justify-between items-center">
              <span><strong>普通用户:</strong> <EMAIL></span>
              <span class="text-blue-600">123456</span>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义样式 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 悬浮动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* 渐入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

/* 输入框聚焦效果 */
input:focus {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.15);
}

/* 按钮悬停效果 */
button[type="submit"]:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px -5px rgba(59, 130, 246, 0.3);
}
</style>
