<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Layout from '@/components/Layout.vue'

const route = useRoute()

// 检查是否需要隐藏Layout
const hideLayout = computed(() => route.meta?.hideLayout === true)
</script>

<template>
  <!-- 独立页面（如登录页）不使用Layout -->
  <router-view v-if="hideLayout" />
  
  <!-- 其他页面使用Layout -->
  <Layout v-else>
    <router-view />
  </Layout>
</template>