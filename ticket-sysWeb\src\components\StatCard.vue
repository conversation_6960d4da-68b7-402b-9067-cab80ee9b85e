<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-center justify-between">
      <div class="flex-1">
        <p class="text-sm font-medium text-gray-600">{{ title }}</p>
        <p class="text-2xl font-bold text-gray-900 mt-1">{{ displayValue }}</p>
        
        <!-- 趋势指示器 -->
        <div v-if="trend" class="flex items-center mt-2">
          <component
            :is="trend.isPositive ? TrendingUp : TrendingDown"
            :class="[
              'w-4 h-4 mr-1',
              trend.isPositive ? 'text-green-500' : 'text-red-500'
            ]"
          />
          <span
            :class="[
              'text-sm font-medium',
              trend.isPositive ? 'text-green-600' : 'text-red-600'
            ]"
          >
            {{ trend.isPositive ? '+' : '-' }}{{ trend.value }}%
          </span>
          <span class="text-sm text-gray-500 ml-1">vs 上月</span>
        </div>
      </div>
      
      <!-- 图标 -->
      <div
        :class="[
          'flex items-center justify-center w-12 h-12 rounded-lg',
          iconBgColor
        ]"
      >
        <component :is="iconComponent" :class="['w-6 h-6', iconColor]" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  FileText,
  Plus,
  Clock,
  DollarSign,
  Users,
  TrendingUp,
  TrendingDown,
  BarChart3,
  CheckCircle
} from 'lucide-vue-next'

// 定义组件属性
interface Props {
  title: string
  value: string | number
  icon: string
  color: 'blue' | 'green' | 'yellow' | 'purple' | 'red' | 'indigo'
  trend?: {
    value: number
    isPositive: boolean
  }
}

const props = defineProps<Props>()

// 图标映射
const iconMap = {
  FileText,
  Plus,
  Clock,
  DollarSign,
  Users,
  BarChart3,
  CheckCircle
}

// 计算属性
const iconComponent = computed(() => {
  return iconMap[props.icon as keyof typeof iconMap] || FileText
})

const displayValue = computed(() => {
  if (typeof props.value === 'number') {
    // 如果是大数字，进行格式化
    if (props.value >= 10000) {
      return (props.value / 10000).toFixed(1) + '万'
    } else if (props.value >= 1000) {
      return (props.value / 1000).toFixed(1) + 'k'
    }
    return props.value.toString()
  }
  return props.value
})

const iconBgColor = computed(() => {
  const colorMap = {
    blue: 'bg-blue-50',
    green: 'bg-green-50',
    yellow: 'bg-yellow-50',
    purple: 'bg-purple-50',
    red: 'bg-red-50',
    indigo: 'bg-indigo-50'
  }
  return colorMap[props.color]
})

const iconColor = computed(() => {
  const colorMap = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    yellow: 'text-yellow-600',
    purple: 'text-purple-600',
    red: 'text-red-600',
    indigo: 'text-indigo-600'
  }
  return colorMap[props.color]
})
</script>