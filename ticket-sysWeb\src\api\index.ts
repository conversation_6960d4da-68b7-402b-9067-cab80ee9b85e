import { get, post, put, del } from '@/utils/request'
import type { 
  User, 
  Ticket, 
  Statistics, 
  LoginForm, 
  RegisterForm, 
  TicketForm, 
  PageQuery, 
  Pagination,
  OperationLog,
  UserCreateForm,
  Admin,
  LoginResponse
} from '@/types'
import { TicketType, TicketStatus } from '@/types'

// 认证相关 API
export const authApi = {
  /**
   * 管理员登录
   * @param data 登录表单数据
   * @returns Promise<LoginResponse>
   */
  login: (data: LoginForm) => post<LoginResponse>('http://localhost:4000/api/admin/login', data),
  
  /**
   * 用户注册
   * @param data 注册表单数据
   * @returns Promise<{user: User, token: string}>
   */
  register: (data: RegisterForm) => post<{user: User, token: string}>('/auth/register', data),
  
  /**
   * 获取当前用户信息
   * @returns Promise<User>
   */
  getCurrentUser: () => get<User>('/auth/me'),
  
  /**
   * 用户登出
   * @returns Promise<void>
   */
  logout: () => post<void>('/auth/logout')
}

// 票据相关 API
export const ticketApi = {
  /**
   * 获取票据列表
   * @param params 查询参数
   * @returns Promise<{list: Ticket[], pagination: Pagination}>
   */
  getTickets: (params?: PageQuery) => get<{list: Ticket[], pagination: Pagination}>('/tickets', { params }),
  
  /**
   * 根据 ID 获取票据详情
   * @param id 票据 ID
   * @returns Promise<Ticket>
   */
  getTicket: (id: string) => get<Ticket>(`/tickets/${id}`),
  getTicketById: (id: string) => get<Ticket>(`/tickets/${id}`),
  
  /**
   * 创建新票据
   * @param data 票据表单数据
   * @returns Promise<Ticket>
   */
  createTicket: (data: TicketForm) => post<Ticket>('/tickets', data),
  
  /**
   * 更新票据信息
   * @param id 票据 ID
   * @param data 更新的票据数据
   * @returns Promise<Ticket>
   */
  updateTicket: (id: string, data: Partial<TicketForm>) => put<Ticket>(`/tickets/${id}`, data),
  
  /**
   * 删除票据
   * @param id 票据 ID
   * @returns Promise<void>
   */
  deleteTicket: (id: string) => del<void>(`/tickets/${id}`),
  
  /**
   * 批量删除票据
   * @param ids 票据 ID 数组
   * @returns Promise<void>
   */
  batchDeleteTickets: (ids: string[]) => post<void>('/tickets/batch-delete', { ids }),
  
  /**
   * 审批票据
   * @param id 票据 ID
   * @param status 审批状态
   * @param comment 审批意见
   * @returns Promise<Ticket>
   */
  approveTicket: (id: string, status: 'approved' | 'rejected', comment?: string) => 
    post<Ticket>(`/tickets/${id}/approve`, { status, comment }),
  
  /**
   * 获取票据操作历史
   * @param id 票据 ID
   * @returns Promise<OperationLog[]>
   */
  getTicketLogs: (id: string) => get<OperationLog[]>(`/tickets/${id}/logs`),
  
  /**
   * 上传票据附件
   * @param file 文件对象
   * @returns Promise<{url: string}>
   */
  uploadAttachment: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return post<{url: string}>('/tickets/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

// 用户管理相关 API
export const userApi = {
  /**
   * 获取用户列表
   * @param params 查询参数
   * @returns Promise<{list: User[], pagination: Pagination}>
   */
  getUsers: (params?: PageQuery) => get<{list: User[], pagination: Pagination}>('/users', { params }),
  
  /**
   * 根据 ID 获取用户详情
   * @param id 用户 ID
   * @returns Promise<User>
   */
  getUserById: (id: string) => get<User>(`/users/${id}`),
  
  /**
   * 创建新用户
   * @param data 用户数据
   * @returns Promise<User>
   */
  createUser: (data: UserCreateForm) => post<User>('/users', data),
  
  /**
   * 更新用户信息
   * @param id 用户 ID
   * @param data 更新的用户数据
   * @returns Promise<User>
   */
  updateUser: (id: string, data: Partial<User>) => put<User>(`/users/${id}`, data),
  
  /**
   * 删除用户
   * @param id 用户 ID
   * @returns Promise<void>
   */
  deleteUser: (id: string) => del<void>(`/users/${id}`),
  
  /**
   * 重置用户密码
   * @param id 用户 ID
   * @param newPassword 新密码
   * @returns Promise<void>
   */
  resetPassword: (id: string, newPassword: string) => 
    post<void>(`/users/${id}/reset-password`, { password: newPassword })
}

// 统计数据相关 API
export const statisticsApi = {
  /**
   * 获取总体统计数据
   * @returns Promise<Statistics>
   */
  getOverallStats: () => get<Statistics>('/statistics/overall'),
  
  /**
   * 获取月度统计数据
   * @param year 年份
   * @param month 月份
   * @returns Promise<any>
   */
  getMonthlyStats: (year: number, month: number) => 
    get<any>(`/statistics/monthly?year=${year}&month=${month}`),
  
  /**
   * 获取年度统计数据
   * @param year 年份
   * @returns Promise<any>
   */
  getYearlyStats: (year: number) => get<any>(`/statistics/yearly?year=${year}`),
  
  /**
   * 获取票据类型分布统计
   * @returns Promise<any>
   */
  getTicketTypeStats: () => get<any>('/statistics/ticket-types'),
  
  /**
   * 获取票据状态分布统计
   * @returns Promise<any>
   */
  getTicketStatusStats: () => get<any>('/statistics/ticket-status')
}

// 系统设置相关 API
export const settingsApi = {
  /**
   * 获取系统设置
   * @returns Promise<any>
   */
  getSettings: () => get<any>('/settings'),
  
  /**
   * 更新系统设置
   * @param settings 设置数据
   * @returns Promise<any>
   */
  updateSettings: (settings: any) => put<any>('/settings', settings),
  
  /**
   * 测试邮件配置
   * @returns Promise<any>
   */
  testEmail: () => post<any>('/settings/test-email'),
  
  /**
   * 测试邮件设置
   * @param settings 邮件设置
   * @returns Promise<any>
   */
  testEmailSettings: (settings: any) => post<any>('/settings/test-email', settings),
  
  /**
   * 获取系统信息
   * @returns Promise<any>
   */
  getSystemInfo: () => get<any>('/settings/system-info'),
  
  /**
   * 导出数据
   * @returns Promise<{url: string}>
   */
  exportData: () => post<{url: string}>('/settings/export'),
  
  /**
   * 清理缓存
   * @returns Promise<void>
   */
  clearCache: () => post<void>('/settings/clear-cache')
}

export const systemApi = {
  /**
   * 获取系统配置
   * @returns Promise<any>
   */
  getSystemConfig: () => get<any>('/system/config'),
  
  /**
   * 更新系统配置
   * @param config 配置数据
   * @returns Promise<any>
   */
  updateSystemConfig: (config: any) => put<any>('/system/config', config),
  
  /**
   * 数据备份
   * @returns Promise<{url: string}>
   */
  backupData: () => post<{url: string}>('/system/backup'),
  
  /**
   * 数据恢复
   * @param file 备份文件
   * @returns Promise<void>
   */
  restoreData: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return post<void>('/system/restore', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 获取系统日志
   * @param params 查询参数
   * @returns Promise<any>
   */
  getSystemLogs: (params?: PageQuery) => get<any>('/system/logs', { params })
}