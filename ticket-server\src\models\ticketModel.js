/**
 * 票据模型
 * 处理票据相关的数据库操作
 */
const mysql = require('mysql2/promise');
const { createPool } = require('../config/db');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

/**
 * 初始化票据表
 * 检查票据表是否存在，如果不存在则创建
 * @returns {Promise<void>}
 */
const initTicketTable = async () => {
  try {
    const pool = await createPool();
    const connection = await pool.getConnection();

    // 检查表是否存在
    const [tables] = await connection.query(
      "SHOW TABLES LIKE 'tickets'"
    );

    // 如果表不存在，创建表
    if (tables.length === 0) {
      logger.info('创建票据表');
      await connection.query(`
        CREATE TABLE tickets (
          id varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '票据ID（UUID）',
          type enum('invoice','receipt','check','contract','other') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'invoice' COMMENT '票据类型：发票/收据/支票/合同/其他',
          title varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '票据标题',
          amount decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '票据金额',
          date date NOT NULL COMMENT '票据日期',
          description text COLLATE utf8mb4_unicode_ci COMMENT '票据描述',
          image_url varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '票据图片URL',
          category varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '票据分类',
          status enum('pending','verified','rejected') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '票据状态：待处理/已验证/已拒绝',
          created_by varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创建者用户ID',
          created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (id),
          KEY idx_type (type),
          KEY idx_status (status),
          KEY idx_category (category),
          KEY idx_date (date),
          KEY idx_amount (amount),
          KEY idx_created_by (created_by),
          KEY idx_created_at (created_at),
          KEY idx_title (title),
          KEY idx_user_status (created_by,status),
          KEY idx_date_amount (date,amount),
          KEY idx_type_status (type,status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='票据主表'
      `);
      
      logger.info('票据表创建成功');
    }

    connection.release();
  } catch (error) {
    logger.error(`初始化票据表失败: ${error.message}`);
    throw error;
  }
};

/**
 * 创建新票据记录
 * @param {Object} ticketData - 票据数据
 * @returns {Promise<Object>} 创建的票据信息
 */
const createTicket = async (ticketData) => {
  try {
    const pool = await createPool();
    const ticketId = uuidv4();
    
    const {
      type = 'invoice',
      title,
      amount = 0.00,
      date,
      description = '',
      image_url,
      category = '',
      status = 'pending',
      created_by
    } = ticketData;

    await pool.query(`
      INSERT INTO tickets (
        id, type, title, amount, date, description, image_url, category, status, created_by
      ) VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
      )
    `, [ticketId, type, title, amount, date, description, image_url, category, status, created_by]);
    
    // 查询并返回创建的票据
    const [rows] = await pool.query('SELECT * FROM tickets WHERE id = ?', [ticketId]);
    
    logger.info(`票据 ${ticketId} 创建成功`);
    return rows[0];
  } catch (error) {
    logger.error(`创建票据失败: ${error.message}`);
    throw error;
  }
};

/**
 * 根据ID查找票据
 * @param {String} ticketId - 票据ID
 * @returns {Promise<Object|null>} 票据信息或null
 */
const findTicketById = async (ticketId) => {
  try {
    const pool = await createPool();
    const [rows] = await pool.query(
      'SELECT * FROM tickets WHERE id = ?',
      [ticketId]
    );
    
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    logger.error(`查找票据失败: ${error.message}`);
    throw error;
  }
};

/**
 * 获取票据列表（带分页和筛选）
 * @param {Object} options - 查询选项
 * @param {number} options.page - 页码（从1开始）
 * @param {number} options.pageSize - 每页数量
 * @param {string} options.type - 票据类型筛选
 * @param {string} options.status - 状态筛选
 * @param {string} options.category - 分类筛选
 * @param {string} options.search - 搜索关键词（标题、描述）
 * @param {string} options.sortBy - 排序字段
 * @param {string} options.sortOrder - 排序顺序（asc/desc）
 * @returns {Promise<Object>} 票据列表和分页信息
 */
const getTicketList = async (options = {}) => {
  try {
    const pool = await createPool();
    
    const {
      page = 1,
      pageSize = 20,
      type,
      status,
      category,
      search,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = options;
    
    // 构建WHERE条件
    const conditions = [];
    const params = [];
    
    if (type) {
      conditions.push('type = ?');
      params.push(type);
    }
    
    if (status) {
      conditions.push('status = ?');
      params.push(status);
    }
    
    if (category) {
      conditions.push('category = ?');
      params.push(category);
    }
    
    if (search) {
      conditions.push('(title LIKE ? OR description LIKE ?)');
      params.push(`%${search}%`, `%${search}%`);
    }
    
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // 验证排序字段和顺序
    const allowedSortFields = ['id', 'type', 'title', 'amount', 'date', 'status', 'created_at', 'updated_at'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const validSortOrder = ['asc', 'desc'].includes(sortOrder.toLowerCase()) ? sortOrder.toLowerCase() : 'desc';
    
    // 计算总数
    const countQuery = `SELECT COUNT(*) as total FROM tickets ${whereClause}`;
    const [countResult] = await pool.query(countQuery, params);
    const total = countResult[0].total;
    
    // 计算分页
    const offset = (page - 1) * pageSize;
    const totalPages = Math.ceil(total / pageSize);
    
    // 查询数据
    const dataQuery = `
      SELECT * FROM tickets 
      ${whereClause} 
      ORDER BY ${validSortBy} ${validSortOrder.toUpperCase()}
      LIMIT ? OFFSET ?
    `;
    const [rows] = await pool.query(dataQuery, [...params, pageSize, offset]);
    
    logger.info(`获取票据列表成功，共 ${total} 条记录，第 ${page} 页`);
    
    return {
      data: rows,
      pagination: {
        page: Number(page),
        pageSize: Number(pageSize),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    logger.error(`获取票据列表失败: ${error.message}`);
    throw error;
  }
};

/**
 * 更新票据信息
 * @param {String} ticketId - 票据ID
 * @param {Object} updateData - 更新数据
 * @returns {Promise<Object>} 更新后的票据信息
 */
const updateTicket = async (ticketId, updateData) => {
  try {
    const pool = await createPool();
    
    const fields = [];
    const values = [];
    
    // 动态构建更新字段
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });
    
    if (fields.length === 0) {
      throw new Error('没有提供更新数据');
    }
    
    values.push(ticketId);
    
    await pool.query(
      `UPDATE tickets SET ${fields.join(', ')} WHERE id = ?`,
      values
    );
    
    // 查询并返回更新后的票据
    const [rows] = await pool.query('SELECT * FROM tickets WHERE id = ?', [ticketId]);
    
    logger.info(`票据 ${ticketId} 更新成功`);
    return rows[0];
  } catch (error) {
    logger.error(`更新票据失败: ${error.message}`);
    throw error;
  }
};

module.exports = {
  initTicketTable,
  createTicket,
  findTicketById,
  getTicketList,
  updateTicket
};