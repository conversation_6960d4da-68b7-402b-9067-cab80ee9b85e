/*
SQLyog Ultimate v12.08 (64 bit)
MySQL - 5.7.18-log : Database - ticket
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`ticket` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `ticket`;

/*Table structure for table `admin_user` */

DROP TABLE IF EXISTS `admin_user`;

CREATE TABLE `admin_user` (
  `admin_id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '管理员唯一ID',
  `username` varchar(50) NOT NULL COMMENT '登录账号（唯一）',
  `password_hash` varchar(255) NOT NULL COMMENT '加密后的密码',
  `salt` varchar(50) NOT NULL COMMENT '密码加密盐值',
  `real_name` varchar(20) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `mobile` varchar(15) NOT NULL DEFAULT '' COMMENT '手机号（唯一）',
  `email` varchar(100) NOT NULL DEFAULT '' COMMENT '邮箱（唯一）',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '头像URL',
  `role_type` enum('super_admin','content_admin','user_admin','audit_admin') NOT NULL DEFAULT 'content_admin' COMMENT '角色类型',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用 2-锁定',
  `last_login_ip` varchar(45) NOT NULL DEFAULT '' COMMENT '最后登录IP',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `creator_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建者ID（0=系统创建）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`admin_id`),
  UNIQUE KEY `uniq_username` (`username`),
  UNIQUE KEY `uniq_mobile` (`mobile`),
  UNIQUE KEY `uniq_email` (`email`),
  KEY `idx_role_status` (`role_type`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='管理员用户表';

/*Data for the table `admin_user` */

insert  into `admin_user`(`admin_id`,`username`,`password_hash`,`salt`,`real_name`,`mobile`,`email`,`avatar`,`role_type`,`status`,`last_login_ip`,`last_login_time`,`creator_id`,`created_at`,`updated_at`) values (1,'admin','d0fcbe83c39186512c37deb27175bc274711ae443b5d8ba6d2df620fa593f860c0486ec2b27bcea57cc501222f6313702542dd4f47bd59a679d2327c9ac980a9','e5f38b12912c1f5b','系统管理员','','<EMAIL>','','super_admin',1,'::1','2025-08-01 10:51:36',0,'2025-07-30 14:40:39','2025-08-01 10:51:36');

/*Table structure for table `users` */

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名，用于登录和显示',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '电子邮箱，用于登录和通知',
  `mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码，用于登录和验证',
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '加密后的密码',
  `salt` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码加密盐值',
  `avatar_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `nickname` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户昵称',
  `bio` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人简介',
  `gender` tinyint(4) DEFAULT '0' COMMENT '性别：0-未知 1-男 2-女',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '账号状态：0-禁用 1-正常 2-未激活',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `register_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `register_ip` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册IP',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_verified` tinyint(4) DEFAULT '0' COMMENT '是否认证：0-未认证 1-已认证',
  `verification_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱/手机验证码',
  `verification_expire` datetime DEFAULT NULL COMMENT '验证码过期时间',
  `role` tinyint(4) DEFAULT '1' COMMENT '用户角色：1-普通用户 2-内容创作者 3-管理员',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_email` (`email`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_status` (`status`),
  KEY `idx_register_time` (`register_time`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

/*Data for the table `users` */

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
