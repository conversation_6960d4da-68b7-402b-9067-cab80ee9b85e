import { defineStore } from 'pinia'
import type { User, Ticket, Statistics, Admin } from '@/types'

/**
 * 用户状态管理
 */
export const useUserStore = defineStore('user', {
  state: () => ({
    user: null as User | null,
    admin: null as Admin | null,
    token: localStorage.getItem('token') as string | null,
    isAuthenticated: !!localStorage.getItem('token')
  }),
  
  actions: {
    /**
     * 设置用户信息
     */
    setUser(user: User | null) {
      this.user = user
      this.updateAuthState()
    },
    
    /**
     * 设置管理员信息
     */
    setAdmin(admin: Admin | null) {
      this.admin = admin
      
      if (admin) {
        localStorage.setItem('admin', JSON.stringify(admin))
      } else {
        localStorage.removeItem('admin')
      }
      
      this.updateAuthState()
    },
    
    /**
     * 设置认证令牌
     */
    setToken(token: string | null) {
      this.token = token
      
      if (token) {
        localStorage.setItem('token', token)
      } else {
        localStorage.removeItem('token')
      }
      
      this.updateAuthState()
    },
    
    /**
     * 更新认证状态
     */
    updateAuthState() {
      this.isAuthenticated = !!(this.token && (this.user || this.admin))
    },
    
    /**
     * 用户登出
     */
    logout() {
      this.user = null
      this.admin = null
      this.token = null
      this.isAuthenticated = false
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('admin')
    },
    
    /**
     * 初始化用户状态
     */
    initUserState() {
      const savedAdmin = localStorage.getItem('admin')
      const savedToken = localStorage.getItem('token')
      
      if (savedAdmin) {
        this.admin = JSON.parse(savedAdmin)
      }
      
      if (savedToken) {
        this.token = savedToken
      }
      
      this.updateAuthState()
    }
  }
})

/**
 * 票据状态管理
 */
export const useTicketStore = defineStore('ticket', {
  state: () => ({
    tickets: [] as Ticket[],
    currentTicket: null as Ticket | null,
    loading: false
  }),
  
  actions: {
    /**
     * 设置票据列表
     */
    setTickets(tickets: Ticket[]) {
      this.tickets = tickets
    },
    
    /**
     * 设置当前票据
     */
    setCurrentTicket(ticket: Ticket | null) {
      this.currentTicket = ticket
    },
    
    /**
     * 设置加载状态
     */
    setLoading(loading: boolean) {
      this.loading = loading
    },
    
    /**
     * 添加票据
     */
    addTicket(ticket: Ticket) {
      this.tickets.unshift(ticket)
    },
    
    /**
     * 更新票据
     */
    updateTicket(id: string, updatedTicket: Partial<Ticket>) {
      const index = this.tickets.findIndex(ticket => ticket.id === id)
      if (index !== -1) {
        this.tickets[index] = { ...this.tickets[index], ...updatedTicket }
      }
    },
    
    /**
     * 删除票据
     */
    removeTicket(id: string) {
      this.tickets = this.tickets.filter(ticket => ticket.id !== id)
    }
  }
})

/**
 * 统计数据状态管理
 */
export const useStatisticsStore = defineStore('statistics', {
  state: () => ({
    statistics: null as Statistics | null
  }),
  
  actions: {
    /**
     * 设置统计数据
     */
    setStatistics(statistics: Statistics) {
      this.statistics = statistics
    }
  }
})

/**
 * 应用状态管理
 */
export const useAppStore = defineStore('app', {
  state: () => ({
    sidebarCollapsed: false,
    theme: (localStorage.getItem('theme') as 'light' | 'dark') || 'light'
  }),
  
  actions: {
    /**
     * 设置侧边栏折叠状态
     */
    setSidebarCollapsed(collapsed: boolean) {
      this.sidebarCollapsed = collapsed
    },
    
    /**
     * 设置主题
     */
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme
      localStorage.setItem('theme', theme)
    }
  }
})