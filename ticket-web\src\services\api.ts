import type { 
  Ticket, 
  TicketStats, 
  VerificationResult, 
  ApiResponse, 
  PaginationParams, 
  TicketQueryParams,
  LoginRequest,
  LoginResponse,
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserQueryParams
} from '@/types'
import { TicketStatus, TicketType } from '@/types'

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟用户数据
let mockUsers: (User & { password: string })[] = [
  {
    id: '1',
    name: '管理员',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'enterprise',
    phone: '***********',
    department: '管理部',
    position: '系统管理员',
    isActive: true,
    avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20avatar%20businessman&image_size=square',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    lastLoginAt: '2024-01-20T10:30:00Z'
  },
  {
    id: '2', 
    name: '张三',
    email: '<EMAIL>',
    password: '123456',
    role: 'user',
    phone: '***********',
    department: '财务部',
    position: '会计',
    isActive: true,
    avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20avatar%20woman&image_size=square',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z',
    lastLoginAt: '2024-01-19T15:20:00Z'
  }
]

// 模拟票据数据
let mockTickets: Ticket[] = [
  {
    id: '1',
    type: TicketType.INVOICE,
    title: '办公用品采购发票',
    amount: 1250.00,
    date: '2024-01-15',
    description: '购买办公桌椅、文具等办公用品',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=office%20supplies%20invoice%20receipt%20document&image_size=landscape_4_3',
    category: '办公用品',
    status: TicketStatus.VERIFIED,
    tags: ['办公用品', '采购'],
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T14:20:00Z',
    createdBy: '张三',
    attachments: [
      {
        id: '1-1',
        name: '发票扫描件.jpg',
        url: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=invoice%20scan%20document&image_size=portrait_4_3',
        type: 'image/jpeg',
        size: 245760
      }
    ]
  },
  {
    id: '2',
    type: TicketType.RECEIPT,
    title: '差旅费报销单',
    amount: 850.50,
    date: '2024-01-14',
    description: '出差北京的交通费和住宿费',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=travel%20expense%20receipt%20document&image_size=landscape_4_3',
    category: '差旅费',
    status: TicketStatus.PENDING,
    tags: ['差旅', '报销'],
    createdAt: '2024-01-14T09:15:00Z',
    updatedAt: '2024-01-14T09:15:00Z',
    createdBy: '李四',
    attachments: [
      {
        id: '2-1',
        name: '火车票.jpg',
        url: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=train%20ticket%20receipt&image_size=landscape_4_3',
        type: 'image/jpeg',
        size: 189440
      },
      {
        id: '2-2',
        name: '酒店发票.pdf',
        url: '#',
        type: 'application/pdf',
        size: 512000
      }
    ]
  },
  {
    id: '3',
    type: TicketType.CONTRACT,
    title: '软件服务合同',
    amount: 50000.00,
    date: '2024-01-10',
    description: '年度软件维护服务合同',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=software%20service%20contract%20document&image_size=portrait_4_3',
    category: '服务费',
    status: TicketStatus.REJECTED,
    tags: ['合同', '软件'],
    createdAt: '2024-01-10T16:45:00Z',
    updatedAt: '2024-01-12T11:30:00Z',
    createdBy: '王五',
    attachments: [
      {
        id: '3-1',
        name: '合同正本.pdf',
        url: '#',
        type: 'application/pdf',
        size: 1024000
      }
    ]
  },
  {
    id: '4',
    type: TicketType.INVOICE,
    title: '设备采购发票',
    amount: 8500.00,
    date: '2024-01-20',
    description: '购买办公电脑和打印机',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=equipment%20purchase%20invoice%20computer%20printer&image_size=landscape_4_3',
    category: '设备采购',
    status: TicketStatus.VERIFIED,
    tags: ['设备', '电脑', '打印机'],
    createdAt: '2024-01-20T11:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z',
    createdBy: '赵六'
  },
  {
    id: '5',
    type: TicketType.RECEIPT,
    title: '餐饮费收据',
    amount: 320.00,
    date: '2024-01-18',
    description: '客户招待用餐费用',
    imageUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=restaurant%20dining%20receipt%20bill&image_size=portrait_4_3',
    category: '招待费',
    status: TicketStatus.PENDING,
    tags: ['餐饮', '招待'],
    createdAt: '2024-01-18T19:30:00Z',
    updatedAt: '2024-01-18T19:30:00Z',
    createdBy: '孙七'
  }
]

/**
 * 获取票据列表
 * @param params 查询参数
 * @returns 票据列表和分页信息
 */
export const getTickets = async (params: Partial<TicketQueryParams> = {}): Promise<ApiResponse<{
  tickets: Ticket[]
  total: number
  page: number
  pageSize: number
}>> => {
  await delay(500)
  
  let filteredTickets = [...mockTickets]
  
  // 应用筛选条件
  if (params.type) {
    filteredTickets = filteredTickets.filter(ticket => ticket.type === params.type)
  }
  
  if (params.status) {
    filteredTickets = filteredTickets.filter(ticket => ticket.status === params.status)
  }
  
  if (params.category) {
    filteredTickets = filteredTickets.filter(ticket => ticket.category === params.category)
  }
  
  if (params.keyword) {
    const searchLower = params.keyword.toLowerCase()
    filteredTickets = filteredTickets.filter(ticket => 
      ticket.title.toLowerCase().includes(searchLower) ||
      ticket.description.toLowerCase().includes(searchLower) ||
      ticket.tags.some(tag => tag.toLowerCase().includes(searchLower))
    )
  }
  
  if (params.startDate) {
    filteredTickets = filteredTickets.filter(ticket => ticket.date >= params.startDate!)
  }
  
  if (params.endDate) {
    filteredTickets = filteredTickets.filter(ticket => ticket.date <= params.endDate!)
  }
  
  if (params.minAmount !== undefined) {
    filteredTickets = filteredTickets.filter(ticket => ticket.amount >= params.minAmount!)
  }
  
  if (params.maxAmount !== undefined) {
    filteredTickets = filteredTickets.filter(ticket => ticket.amount <= params.maxAmount!)
  }
  
  // 排序
  if (params.sortBy) {
    filteredTickets.sort((a, b) => {
      const aValue = a[params.sortBy as keyof Ticket]
      const bValue = b[params.sortBy as keyof Ticket]
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return params.sortOrder === 'desc' 
          ? bValue.localeCompare(aValue)
          : aValue.localeCompare(bValue)
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return params.sortOrder === 'desc' ? bValue - aValue : aValue - bValue
      }
      
      return 0
    })
  }
  
  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 20
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedTickets = filteredTickets.slice(startIndex, endIndex)
  
  return {
    code: 200,
    message: '获取票据列表成功',
    data: {
      data: {
        tickets: paginatedTickets,
        total: filteredTickets.length,
        page,
        pageSize
      }
    }
  }
}

/**
 * 根据ID获取票据详情
 * @param id 票据ID
 * @returns 票据详情
 */
export const getTicketById = async (id: string): Promise<ApiResponse<Ticket>> => {
  await delay(300)
  
  const ticket = mockTickets.find(t => t.id === id)
  
  if (!ticket) {
    return {
      code: 404,
      message: '票据不存在',
      data: {
        data: null
      }
    }
  }
  
  return {
    code: 200,
    message: '获取票据详情成功',
    data: {
      data: ticket
    }
  }
}

/**
 * 创建新票据
 * @param ticketData 票据数据
 * @returns 创建的票据
 */
export const createTicket = async (ticketData: Omit<Ticket, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Ticket>> => {
  await delay(800)
  
  const newTicket: Ticket = {
    ...ticketData,
    id: Date.now().toString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  mockTickets.unshift(newTicket)
  
  return {
    code: 201,
    message: '票据创建成功',
    data: {
      data: newTicket
    }
  }
}

/**
 * 更新票据
 * @param id 票据ID
 * @param ticketData 更新的票据数据
 * @returns 更新后的票据
 */
export const updateTicket = async (id: string, ticketData: Partial<Ticket>): Promise<ApiResponse<Ticket>> => {
  await delay(600)
  
  const index = mockTickets.findIndex(t => t.id === id)
  
  if (index === -1) {
    return {
      code: 404,
      message: '票据不存在',
      data: {
        data: null
      }
    }
  }
  
  const updatedTicket = {
    ...mockTickets[index],
    ...ticketData,
    updatedAt: new Date().toISOString()
  }
  
  mockTickets[index] = updatedTicket
  
  return {
    code: 200,
    message: '票据更新成功',
    data: {
      data: updatedTicket
    }
  }
}

/**
 * 删除票据
 * @param id 票据ID
 * @returns 删除结果
 */
export const deleteTicket = async (id: string): Promise<ApiResponse<boolean>> => {
  await delay(400)
  
  const index = mockTickets.findIndex(t => t.id === id)
  
  if (index === -1) {
    return {
      code: 404,
      message: '票据不存在',
      data: {
        data: false
      }
    }
  }
  
  mockTickets.splice(index, 1)
  
  return {
    code: 200,
    message: '票据删除成功',
    data: {
      data: true
    }
  }
}

/**
 * 批量删除票据
 * @param ids 票据ID数组
 * @returns 删除结果
 */
export const deleteTickets = async (ids: string[]): Promise<ApiResponse<number>> => {
  await delay(600)
  
  let deletedCount = 0
  
  ids.forEach(id => {
    const index = mockTickets.findIndex(t => t.id === id)
    if (index !== -1) {
      mockTickets.splice(index, 1)
      deletedCount++
    }
  })
  
  return {
    code: 200,
    message: `成功删除 ${deletedCount} 张票据`,
    data: {
      data: deletedCount
    }
  }
}

/**
 * 核对票据
 * @param id 票据ID
 * @returns 核对结果
 */
export const verifyTicket = async (id: string): Promise<ApiResponse<VerificationResult>> => {
  await delay(1500) // 模拟核对过程
  
  const ticket = mockTickets.find(t => t.id === id)
  
  if (!ticket) {
    return {
      code: 404,
      message: '票据不存在',
      data: {
        data: null
      }
    }
  }
  
  // 模拟核对结果
  const isValid = Math.random() > 0.2 // 80% 通过率
  const confidence = 0.7 + Math.random() * 0.3 // 70-100% 置信度
  
  const result: VerificationResult = {
    ticketId: id,
    isValid,
    confidence,
    issues: isValid ? [] : [
      '金额与发票不符',
      '日期格式错误'
    ],
    verifiedAt: new Date().toISOString(),
    verifiedBy: 'system'
  }
  
  // 更新票据状态
  const index = mockTickets.findIndex(t => t.id === id)
  if (index !== -1) {
    mockTickets[index] = {
      ...mockTickets[index],
      status: isValid ? TicketStatus.VERIFIED : TicketStatus.REJECTED,
      updatedAt: new Date().toISOString()
    }
  }
  
  return {
    code: 200,
    message: isValid ? '票据核对通过' : '票据核对未通过',
    data: {
      data: result
    }
  }
}

/**
 * 批量核对票据
 * @param ids 票据ID数组
 * @returns 核对结果数组
 */
export const verifyTickets = async (ids: string[]): Promise<ApiResponse<VerificationResult[]>> => {
  await delay(2000) // 模拟批量核对过程
  
  const results: VerificationResult[] = []
  
  for (const id of ids) {
    const ticket = mockTickets.find(t => t.id === id)
    if (!ticket) continue
    
    const isValid = Math.random() > 0.2
    const confidence = 0.7 + Math.random() * 0.3
    
    const result: VerificationResult = {
      ticketId: id,
      isValid,
      confidence,
      issues: isValid ? [] : ['批量核对发现问题'],
      verifiedAt: new Date().toISOString(),
      verifiedBy: 'system'
    }
    
    results.push(result)
    
    // 更新票据状态
    const index = mockTickets.findIndex(t => t.id === id)
    if (index !== -1) {
      mockTickets[index] = {
        ...mockTickets[index],
        status: isValid ? TicketStatus.VERIFIED : TicketStatus.REJECTED,
        updatedAt: new Date().toISOString()
      }
    }
  }
  
  return {
    code: 200,
    message: `批量核对完成，共处理 ${results.length} 张票据`,
    data: {
      data: results
    }
  }
}

/**
 * 获取票据统计数据
 * @returns 统计数据
 */
export const getTicketStats = async (): Promise<ApiResponse<TicketStats>> => {
  await delay(400)
  
  const stats: TicketStats = {
    total: mockTickets.length,
    verified: mockTickets.filter(t => t.status === TicketStatus.VERIFIED).length,
    pending: mockTickets.filter(t => t.status === TicketStatus.PENDING).length,
    rejected: mockTickets.filter(t => t.status === TicketStatus.REJECTED).length,
    totalAmount: mockTickets.reduce((sum, ticket) => sum + ticket.amount, 0),
    averageAmount: mockTickets.length > 0 
      ? mockTickets.reduce((sum, ticket) => sum + ticket.amount, 0) / mockTickets.length 
      : 0
  }
  
  return {
    code: 200,
    message: '获取统计数据成功',
    data: {
      data: stats
    }
  }
}

/**
 * 获取票据类型分布
 * @returns 类型分布数据
 */
export const getTicketTypeDistribution = async (): Promise<ApiResponse<Array<{ type: string; count: number; amount: number }>>> => {
  await delay(300)
  
  const distribution = mockTickets.reduce((acc, ticket) => {
    const existing = acc.find(item => item.type === ticket.type)
    if (existing) {
      existing.count++
      existing.amount += ticket.amount
    } else {
      acc.push({
        type: ticket.type,
        count: 1,
        amount: ticket.amount
      })
    }
    return acc
  }, [] as Array<{ type: string; count: number; amount: number }>)
  
  return {
    code: 200,
    message: '获取类型分布成功',
    data: {
      data: distribution
    }
  }
}

/**
 * 获取月度趋势数据
 * @returns 月度趋势数据
 */
export const getMonthlyTrend = async (): Promise<ApiResponse<Array<{ month: string; count: number; amount: number }>>> => {
  await delay(400)
  
  // 模拟最近6个月的数据
  const months = []
  const now = new Date()
  
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    
    // 模拟数据
    const count = Math.floor(Math.random() * 20) + 5
    const amount = Math.floor(Math.random() * 50000) + 10000
    
    months.push({
      month: monthStr,
      count,
      amount
    })
  }
  
  return {
    code: 200,
    message: '获取月度趋势成功',
    data: {
      data: months
    }
  }
}

/**
 * 导出票据数据
 * @param format 导出格式
 * @param ids 要导出的票据ID（可选，不传则导出全部）
 * @returns 导出结果
 */
export const exportTickets = async (format: 'excel' | 'csv' | 'pdf', ids?: string[]): Promise<ApiResponse<{ url: string; filename: string }>> => {
  await delay(1000)
  
  const ticketsToExport = ids 
    ? mockTickets.filter(t => ids.includes(t.id))
    : mockTickets
  
  // 模拟导出文件
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
  const filename = `tickets_${timestamp}.${format}`
  const url = `#` // 实际应用中这里应该是文件下载链接
  
  return {
    code: 200,
    message: `成功导出 ${ticketsToExport.length} 张票据`,
    data: {
      data: { url, filename }
    }
  }
}

/**
 * 上传并识别票据图片
 * @param file 图片文件
 * @returns OCR识别结果
 */
export const uploadAndRecognizeTicket = async (file: File): Promise<ApiResponse<{
  title: string
  amount: number
  date: string
  type: string
  confidence: number
}>> => {
  await delay(2000) // 模拟OCR处理时间
  
  // 模拟OCR识别结果
  const mockResults = [
    {
      title: '增值税专用发票',
      amount: 1580.00,
      date: new Date().toISOString().slice(0, 10),
      type: TicketType.INVOICE,
      confidence: 0.95
    },
    {
      title: '餐饮费收据',
      amount: 268.50,
      date: new Date().toISOString().slice(0, 10),
      type: TicketType.RECEIPT,
      confidence: 0.88
    },
    {
      title: '服务合同',
      amount: 15000.00,
      date: new Date().toISOString().slice(0, 10),
      type: TicketType.CONTRACT,
      confidence: 0.92
    }
  ]
  
  const result = mockResults[Math.floor(Math.random() * mockResults.length)]
  
  return {
    code: 200,
    message: 'OCR识别完成',
    data: {
      data: result
    }
  }
}

/**
 * 用户登录
 * @param loginData 登录数据
 * @returns 登录结果
 */
export const login = async (loginData: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
  await delay(800) // 模拟登录验证过程
  
  // 验证用户凭据
  const user = mockUsers.find(u => 
    u.email === loginData.email && u.password === loginData.password && u.isActive
  )
  
  if (!user) {
    return {
      code: 401,
      message: '邮箱或密码错误',
      data: {
        data: null
      }
    }
  }
  
  // 生成模拟token
  const token = `mock_token_${user.id}_${Date.now()}`
  const expiresIn = 24 * 60 * 60 * 1000 // 24小时
  
  // 更新最后登录时间
  const userIndex = mockUsers.findIndex(u => u.id === user.id)
  if (userIndex !== -1) {
    mockUsers[userIndex].lastLoginAt = new Date().toISOString()
  }

  const loginResponse: LoginResponse = {
    user: {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      avatar: user.avatar,
      phone: user.phone,
      department: user.department,
      position: user.position,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.lastLoginAt
    },
    token,
    expiresIn
  }
  
  return {
    code: 200,
    message: '登录成功',
    data: {
      data: loginResponse
    }
  }
}

/**
 * 用户登出
 * @returns 登出结果
 */
export const logout = async (): Promise<ApiResponse<boolean>> => {
  await delay(300)
  
  return {
    code: 200,
    message: '登出成功',
    data: {
      data: true
    }
  }
}

/**
 * 获取当前用户信息
 * @returns 用户信息
 */
export const getCurrentUser = async (): Promise<ApiResponse<User>> => {
  await delay(400)
  
  // 模拟从token获取用户信息（实际应用中应该从token中解析用户ID）
  const mockUser = mockUsers[0]
  const { password, ...userInfo } = mockUser
  
  return {
    code: 200,
    message: '获取用户信息成功',
    data: {
      data: userInfo
    }
  }
}

/**
 * 获取用户列表
 * @param params 查询参数
 * @returns 用户列表和分页信息
 */
export const getUsers = async (params: Partial<UserQueryParams> = {}): Promise<ApiResponse<{
  users: User[]
  total: number
  page: number
  pageSize: number
}>> => {
  await delay(500)
  
  let filteredUsers = mockUsers.map(({ password, ...user }) => user)
  
  // 应用筛选条件
  if (params.role) {
    filteredUsers = filteredUsers.filter(user => user.role === params.role)
  }
  
  if (params.isActive !== undefined) {
    filteredUsers = filteredUsers.filter(user => user.isActive === params.isActive)
  }
  
  if (params.keyword) {
    const searchLower = params.keyword.toLowerCase()
    filteredUsers = filteredUsers.filter(user => 
      user.name.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      (user.department && user.department.toLowerCase().includes(searchLower)) ||
      (user.position && user.position.toLowerCase().includes(searchLower))
    )
  }
  
  // 排序
  if (params.sortBy) {
    filteredUsers.sort((a, b) => {
      const aValue = a[params.sortBy as keyof User]
      const bValue = b[params.sortBy as keyof User]
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return params.sortOrder === 'desc' 
          ? bValue.localeCompare(aValue)
          : aValue.localeCompare(bValue)
      }
      
      return 0
    })
  }
  
  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 20
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex)
  
  return {
    code: 200,
    message: '获取用户列表成功',
    data: {
      data: {
        users: paginatedUsers,
        total: filteredUsers.length,
        page,
        pageSize
      }
    }
  }
}

/**
 * 创建新用户
 * @param userData 用户数据
 * @returns 创建的用户
 */
export const createUser = async (userData: CreateUserRequest): Promise<ApiResponse<User>> => {
  await delay(800)
  
  // 检查邮箱是否已存在
  const existingUser = mockUsers.find(u => u.email === userData.email)
  if (existingUser) {
    return {
      code: 400,
      message: '邮箱已存在',
      data: {
        data: null
      }
    }
  }
  
  const newUser: User & { password: string } = {
    ...userData,
    id: Date.now().toString(),
    isActive: true,
    avatar: `https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20avatar%20${userData.role === 'enterprise' ? 'businessman' : 'person'}&image_size=square`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  mockUsers.push(newUser)
  
  const { password, ...userResponse } = newUser
  
  return {
    code: 201,
    message: '用户创建成功',
    data: {
      data: userResponse
    }
  }
}

/**
 * 更新用户
 * @param id 用户ID
 * @param userData 更新的用户数据
 * @returns 更新后的用户
 */
export const updateUser = async (id: string, userData: UpdateUserRequest): Promise<ApiResponse<User>> => {
  await delay(600)
  
  const index = mockUsers.findIndex(u => u.id === id)
  
  if (index === -1) {
    return {
      code: 404,
      message: '用户不存在',
      data: {
        data: null
      }
    }
  }
  
  // 如果更新邮箱，检查是否重复
  if (userData.email && userData.email !== mockUsers[index].email) {
    const existingUser = mockUsers.find(u => u.email === userData.email && u.id !== id)
    if (existingUser) {
      return {
        code: 400,
        message: '邮箱已存在',
        data: {
          data: null
        }
      }
    }
  }
  
  const updatedUser = {
    ...mockUsers[index],
    ...userData,
    updatedAt: new Date().toISOString()
  }
  
  mockUsers[index] = updatedUser
  
  const { password, ...userResponse } = updatedUser
  
  return {
    code: 200,
    message: '用户更新成功',
    data: {
      data: userResponse
    }
  }
}

/**
 * 删除用户
 * @param id 用户ID
 * @returns 删除结果
 */
export const deleteUser = async (id: string): Promise<ApiResponse<boolean>> => {
  await delay(400)
  
  const index = mockUsers.findIndex(u => u.id === id)
  
  if (index === -1) {
    return {
      code: 404,
      message: '用户不存在',
      data: {
        data: false
      }
    }
  }
  
  // 不允许删除管理员
  if (mockUsers[index].role === 'enterprise' && mockUsers[index].email === '<EMAIL>') {
    return {
      code: 403,
      message: '不能删除系统管理员',
      data: {
        data: false
      }
    }
  }
  
  mockUsers.splice(index, 1)
  
  return {
    code: 200,
    message: '用户删除成功',
    data: {
      data: true
    }
  }
}

/**
 * 重置用户密码
 * @param id 用户ID
 * @param newPassword 新密码
 * @returns 重置结果
 */
export const resetUserPassword = async (id: string, newPassword: string): Promise<ApiResponse<boolean>> => {
  await delay(500)
  
  const index = mockUsers.findIndex(u => u.id === id)
  
  if (index === -1) {
    return {
      code: 404,
      message: '用户不存在',
      data: {
        data: false
      }
    }
  }
  
  mockUsers[index].password = newPassword
  mockUsers[index].updatedAt = new Date().toISOString()
  
  return {
    code: 200,
    message: '密码重置成功',
    data: {
      data: true
    }
  }
}