import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores'
import AppLayout from '@/components/Layout/AppLayout.vue'
import HomePage from '@/pages/HomePage.vue'
import LoginPage from '@/pages/LoginPage.vue'
import RegisterPage from '@/pages/RegisterPage.vue'
import TicketListPage from '@/pages/TicketListPage.vue'
import TicketEditPage from '@/pages/TicketEditPage.vue'
import TicketDetailPage from '@/pages/TicketDetailPage.vue'
import UserManagePage from '@/pages/UserManagePage.vue'
import SettingsPage from '@/pages/SettingsPage.vue'

// 定义路由配置
const routes: RouteRecordRaw[] = [
  // 认证相关路由
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterPage,
    meta: { requiresGuest: true }
  },
  
  // 主应用路由
  {
    path: '/',
    component: AppLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'home',
        component: HomePage
      },
      {
        path: 'tickets',
        name: 'tickets',
        component: TicketListPage
      },
      {
        path: 'tickets/create',
        name: 'ticket-create',
        component: TicketEditPage
      },
      {
        path: 'tickets/:id',
        name: 'ticket-detail',
        component: TicketDetailPage
      },
      {
        path: 'tickets/:id/edit',
        name: 'ticket-edit',
        component: TicketEditPage
      },
      {
        path: 'users',
        name: 'users',
        component: UserManagePage,
        meta: { requiresAdmin: true }
      },
      {
        path: 'settings',
        name: 'settings',
        component: SettingsPage,
        meta: { requiresAdmin: true }
      }
    ]
  },
  
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: {
      template: `
        <div class="min-h-screen flex items-center justify-center bg-gray-50">
          <div class="text-center">
            <h1 class="text-6xl font-bold text-gray-900">404</h1>
            <p class="text-xl text-gray-600 mt-4">页面未找到</p>
            <router-link to="/" class="mt-6 inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              返回首页
            </router-link>
          </div>
        </div>
      `
    },
    meta: {}
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
})

/**
 * 路由守卫
 */
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  const isAuthenticated = userStore.isAuthenticated
  const user = userStore.user
  const admin = userStore.admin
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
    return
  }
  
  // 检查是否需要游客状态（已登录用户不能访问登录/注册页）
  if (to.meta.requiresGuest && isAuthenticated) {
    next('/')
    return
  }
  
  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && !admin && user?.role !== 'admin') {
    next('/')
    return
  }
  
  next()
})

export default router
