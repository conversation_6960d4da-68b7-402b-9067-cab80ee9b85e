<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useTicketStore } from '@/stores/useTicketStore'
import { TicketType, TicketStatus } from '@/types'
import { 
  Bar<PERSON>hart3, 
  <PERSON><PERSON>hart, 
  TrendingUp, 
  Calendar, 
  Download, 
  Filter, 
  RefreshCw,
  DollarSign,
  FileText,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Settings
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const ticketStore = useTicketStore()

// 统计数据
const statistics = ref({
  overview: {
    totalTickets: 0,
    totalAmount: 0,
    verifiedTickets: 0,
    pendingTickets: 0,
    rejectedTickets: 0,
    averageAmount: 0,
    monthlyGrowth: 0
  },
  typeDistribution: [] as Array<{ type: string; count: number; amount: number }>,
  statusDistribution: [] as Array<{ status: string; count: number; percentage: number }>,
  monthlyTrend: [] as Array<{ month: string; count: number; amount: number }>,
  categoryStats: [] as Array<{ category: string; count: number; amount: number }>,
  verificationStats: {
    totalVerified: 0,
    successRate: 0,
    averageConfidence: 0,
    commonIssues: [] as Array<{ issue: string; count: number }>
  }
})

// 筛选条件
const filters = reactive({
  dateRange: {
    start: '',
    end: ''
  },
  type: '',
  status: '',
  category: '',
  amountRange: {
    min: '',
    max: ''
  }
})

// 图表配置
const chartOptions = reactive({
  showDataLabels: true,
  animationEnabled: true,
  theme: 'light'
})

// 加载状态
const loading = ref(false)

// 显示筛选面板
const showFilters = ref(false)

// 显示设置面板
const showSettings = ref(false)

/**
 * 组件挂载时获取统计数据
 */
onMounted(() => {
  initializeDateRange()
  fetchStatistics()
})

/**
 * 初始化日期范围（默认最近30天）
 */
const initializeDateRange = () => {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - 30)
  
  filters.dateRange.end = end.toISOString().split('T')[0]
  filters.dateRange.start = start.toISOString().split('T')[0]
}

/**
 * 获取统计数据
 */
const fetchStatistics = async () => {
  try {
    loading.value = true
    
    // 模拟获取统计数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 生成模拟数据
    statistics.value = {
      overview: {
        totalTickets: 1248,
        totalAmount: 2456789.50,
        verifiedTickets: 1089,
        pendingTickets: 127,
        rejectedTickets: 32,
        averageAmount: 1968.75,
        monthlyGrowth: 12.5
      },
      typeDistribution: [
        { type: '发票', count: 567, amount: 1234567.89 },
        { type: '收据', count: 423, amount: 876543.21 },
        { type: '支票', count: 189, amount: 234567.89 },
        { type: '其他', count: 69, amount: 111110.51 }
      ],
      statusDistribution: [
        { status: '已验证', count: 1089, percentage: 87.3 },
        { status: '待处理', count: 127, percentage: 10.2 },
        { status: '已拒绝', count: 32, percentage: 2.5 }
      ],
      monthlyTrend: [
        { month: '1月', count: 98, amount: 195678.90 },
        { month: '2月', count: 112, amount: 223456.78 },
        { month: '3月', count: 134, amount: 267890.12 },
        { month: '4月', count: 156, amount: 312345.67 },
        { month: '5月', count: 178, amount: 356789.01 },
        { month: '6月', count: 203, amount: 406123.45 },
        { month: '7月', count: 189, amount: 378901.23 },
        { month: '8月', count: 178, amount: 356789.01 }
      ],
      categoryStats: [
        { category: '办公用品', count: 234, amount: 456789.12 },
        { category: '差旅费', count: 189, amount: 378901.23 },
        { category: '餐饮费', count: 156, amount: 312345.67 },
        { category: '交通费', count: 134, amount: 267890.12 },
        { category: '其他', count: 535, amount: 1041863.36 }
      ],
      verificationStats: {
        totalVerified: 1089,
        successRate: 97.1,
        averageConfidence: 0.89,
        commonIssues: [
          { issue: '图片质量较低', count: 23 },
          { issue: '信息不完整', count: 18 },
          { issue: '格式不规范', count: 15 },
          { issue: '金额不匹配', count: 12 },
          { issue: '日期异常', count: 8 }
        ]
      }
    }
    
  } catch (error) {
    toast.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

/**
 * 应用筛选条件
 */
const applyFilters = () => {
  fetchStatistics()
  showFilters.value = false
  toast.success('筛选条件已应用')
}

/**
 * 重置筛选条件
 */
const resetFilters = () => {
  Object.assign(filters, {
    dateRange: {
      start: '',
      end: ''
    },
    type: '',
    status: '',
    category: '',
    amountRange: {
      min: '',
      max: ''
    }
  })
  initializeDateRange()
  fetchStatistics()
}

/**
 * 导出统计报告
 */
const exportReport = () => {
  // 模拟导出功能
  toast.success('正在生成统计报告...')
}

/**
 * 刷新数据
 */
const refreshData = () => {
  fetchStatistics()
  toast.success('数据已刷新')
}

/**
 * 格式化金额
 * @param amount 金额
 */
const formatAmount = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

/**
 * 格式化百分比
 * @param value 数值
 */
const formatPercentage = (value: number) => {
  return `${value.toFixed(1)}%`
}

/**
 * 获取状态颜色
 * @param status 状态
 */
const getStatusColor = (status: string) => {
  switch (status) {
    case '已验证':
      return 'text-green-600'
    case '待处理':
      return 'text-yellow-600'
    case '已拒绝':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

/**
 * 获取类型颜色
 * @param type 类型
 */
const getTypeColor = (type: string) => {
  const colors = {
    '发票': 'bg-blue-100 text-blue-800',
    '收据': 'bg-green-100 text-green-800',
    '支票': 'bg-purple-100 text-purple-800',
    '其他': 'bg-gray-100 text-gray-800'
  }
  return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

// 计算属性
const totalAmount = computed(() => {
  return statistics.value.overview.totalAmount
})

const successRate = computed(() => {
  const { verifiedTickets, totalTickets } = statistics.value.overview
  return totalTickets > 0 ? (verifiedTickets / totalTickets * 100).toFixed(1) : '0'
})

const monthlyGrowthClass = computed(() => {
  const growth = statistics.value.overview.monthlyGrowth
  return growth >= 0 ? 'text-green-600' : 'text-red-600'
})
</script>

<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">统计分析</h1>
        <p class="text-gray-600">票据数据统计与分析报告</p>
      </div>
      <div class="mt-4 sm:mt-0 flex items-center space-x-2">
        <button 
          @click="showFilters = !showFilters"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <Filter class="w-4 h-4 mr-2" />
          筛选
        </button>
        <button 
          @click="showSettings = !showSettings"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <Settings class="w-4 h-4 mr-2" />
          设置
        </button>
        <button 
          @click="refreshData"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
        >
          <RefreshCw :class="['w-4 h-4 mr-2', { 'animate-spin': loading }]" />
          刷新
        </button>
        <button 
          @click="exportReport"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Download class="w-4 h-4 mr-2" />
          导出报告
        </button>
      </div>
    </div>

    <!-- 筛选面板 -->
    <div v-if="showFilters" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
          <input 
            v-model="filters.dateRange.start"
            type="date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
          <input 
            v-model="filters.dateRange.end"
            type="date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">票据类型</label>
          <select 
            v-model="filters.type"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部类型</option>
            <option value="invoice">发票</option>
            <option value="receipt">收据</option>
            <option value="check">支票</option>
            <option value="other">其他</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <select 
            v-model="filters.status"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部状态</option>
            <option value="verified">已验证</option>
            <option value="pending">待处理</option>
            <option value="rejected">已拒绝</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">最小金额</label>
          <input 
            v-model="filters.amountRange.min"
            type="number"
            placeholder="0"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">最大金额</label>
          <input 
            v-model="filters.amountRange.max"
            type="number"
            placeholder="无限制"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
        </div>
      </div>
      <div class="flex justify-end space-x-2 mt-4">
        <button 
          @click="resetFilters"
          class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          重置
        </button>
        <button 
          @click="applyFilters"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          应用筛选
        </button>
      </div>
    </div>

    <!-- 图表设置面板 -->
    <div v-if="showSettings" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">图表设置</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="flex items-center justify-between">
          <label class="text-sm font-medium text-gray-700">显示数据标签</label>
          <input 
            v-model="chartOptions.showDataLabels"
            type="checkbox" 
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          >
        </div>
        <div class="flex items-center justify-between">
          <label class="text-sm font-medium text-gray-700">启用动画</label>
          <input 
            v-model="chartOptions.animationEnabled"
            type="checkbox" 
            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          >
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">主题</label>
          <select 
            v-model="chartOptions.theme"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="light">浅色</option>
            <option value="dark">深色</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <FileText class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">总票据数</div>
            <div class="text-2xl font-bold text-gray-900">{{ statistics.overview.totalTickets.toLocaleString() }}</div>
            <div :class="['text-sm', monthlyGrowthClass]">
              <TrendingUp class="w-4 h-4 inline mr-1" />
              {{ formatPercentage(statistics.overview.monthlyGrowth) }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <DollarSign class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">总金额</div>
            <div class="text-2xl font-bold text-gray-900">{{ formatAmount(totalAmount) }}</div>
            <div class="text-sm text-gray-600">
              平均: {{ formatAmount(statistics.overview.averageAmount) }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <CheckCircle class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">验证成功率</div>
            <div class="text-2xl font-bold text-gray-900">{{ successRate }}%</div>
            <div class="text-sm text-gray-600">
              {{ statistics.overview.verifiedTickets }} / {{ statistics.overview.totalTickets }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Clock class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-gray-500">待处理</div>
            <div class="text-2xl font-bold text-gray-900">{{ statistics.overview.pendingTickets }}</div>
            <div class="text-sm text-red-600">
              拒绝: {{ statistics.overview.rejectedTickets }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 票据类型分布 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900">票据类型分布</h2>
          <PieChart class="w-5 h-5 text-gray-400" />
        </div>
        <div class="space-y-4">
          <div 
            v-for="item in statistics.typeDistribution" 
            :key="item.type"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <span 
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  getTypeColor(item.type)
                ]"
              >
                {{ item.type }}
              </span>
              <span class="text-sm text-gray-600">{{ item.count }} 个</span>
            </div>
            <div class="text-sm font-medium text-gray-900">
              {{ formatAmount(item.amount) }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 状态分布 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900">状态分布</h2>
          <BarChart3 class="w-5 h-5 text-gray-400" />
        </div>
        <div class="space-y-4">
          <div 
            v-for="item in statistics.statusDistribution" 
            :key="item.status"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <div class="flex items-center">
                <CheckCircle v-if="item.status === '已验证'" class="w-4 h-4 text-green-600 mr-2" />
                <Clock v-else-if="item.status === '待处理'" class="w-4 h-4 text-yellow-600 mr-2" />
                <XCircle v-else class="w-4 h-4 text-red-600 mr-2" />
                <span class="text-sm font-medium text-gray-900">{{ item.status }}</span>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-600">{{ item.count }}</span>
              <span :class="['text-sm font-medium', getStatusColor(item.status)]">
                {{ formatPercentage(item.percentage) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 月度趋势 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">月度趋势</h2>
        <TrendingUp class="w-5 h-5 text-gray-400" />
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead>
            <tr class="border-b border-gray-200">
              <th class="text-left py-2 text-sm font-medium text-gray-500">月份</th>
              <th class="text-right py-2 text-sm font-medium text-gray-500">票据数量</th>
              <th class="text-right py-2 text-sm font-medium text-gray-500">总金额</th>
              <th class="text-right py-2 text-sm font-medium text-gray-500">平均金额</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr 
              v-for="item in statistics.monthlyTrend" 
              :key="item.month"
              class="hover:bg-gray-50"
            >
              <td class="py-3 text-sm font-medium text-gray-900">{{ item.month }}</td>
              <td class="py-3 text-sm text-gray-600 text-right">{{ item.count.toLocaleString() }}</td>
              <td class="py-3 text-sm text-gray-600 text-right">{{ formatAmount(item.amount) }}</td>
              <td class="py-3 text-sm text-gray-600 text-right">
                {{ formatAmount(item.count > 0 ? item.amount / item.count : 0) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 分类统计 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900">分类统计</h2>
          <BarChart3 class="w-5 h-5 text-gray-400" />
        </div>
        <div class="space-y-3">
          <div 
            v-for="item in statistics.categoryStats" 
            :key="item.category"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-md"
          >
            <div>
              <div class="text-sm font-medium text-gray-900">{{ item.category }}</div>
              <div class="text-xs text-gray-600">{{ item.count }} 个票据</div>
            </div>
            <div class="text-sm font-medium text-gray-900">
              {{ formatAmount(item.amount) }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 核对统计 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900">核对统计</h2>
          <CheckCircle class="w-5 h-5 text-gray-400" />
        </div>
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-3 bg-green-50 rounded-md">
              <div class="text-2xl font-bold text-green-600">{{ statistics.verificationStats.totalVerified }}</div>
              <div class="text-sm text-gray-600">已核对</div>
            </div>
            <div class="text-center p-3 bg-blue-50 rounded-md">
              <div class="text-2xl font-bold text-blue-600">{{ formatPercentage(statistics.verificationStats.successRate) }}</div>
              <div class="text-sm text-gray-600">成功率</div>
            </div>
          </div>
          
          <div class="text-center p-3 bg-purple-50 rounded-md">
            <div class="text-2xl font-bold text-purple-600">{{ formatPercentage(statistics.verificationStats.averageConfidence * 100) }}</div>
            <div class="text-sm text-gray-600">平均置信度</div>
          </div>
          
          <div>
            <div class="text-sm font-medium text-gray-900 mb-2">常见问题</div>
            <div class="space-y-1">
              <div 
                v-for="issue in statistics.verificationStats.commonIssues.slice(0, 3)" 
                :key="issue.issue"
                class="flex justify-between text-xs"
              >
                <span class="text-gray-600">{{ issue.issue }}</span>
                <span class="text-gray-900 font-medium">{{ issue.count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>