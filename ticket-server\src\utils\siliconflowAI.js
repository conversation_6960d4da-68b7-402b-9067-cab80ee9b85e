/**
 * 硅基流动AI服务
 * 对接硅基流动的Qwen2.5-VL-32B-Instruct大模型进行票据识别
 */
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const logger = require('./logger');
const { AppError } = require('../middlewares/error');

// 硅基流动API配置
const SILICONFLOW_API_URL = 'https://api.siliconflow.cn/v1/chat/completions';
const SILICONFLOW_MODEL = 'Qwen/Qwen2.5-VL-32B-Instruct';

/**
 * 将图片文件转换为base64格式
 * @param {String} imagePath - 图片文件路径
 * @returns {Promise<String>} base64编码的图片数据
 */
const imageToBase64 = async (imagePath) => {
  try {
    const imageBuffer = fs.readFileSync(imagePath);
    const base64Image = imageBuffer.toString('base64');
    const mimeType = getMimeType(imagePath);
    return `data:${mimeType};base64,${base64Image}`;
  } catch (error) {
    logger.error(`图片转换base64失败: ${error.message}`);
    throw new AppError('图片处理失败', 500);
  }
};

/**
 * 根据文件扩展名获取MIME类型
 * @param {String} filePath - 文件路径
 * @returns {String} MIME类型
 */
const getMimeType = (filePath) => {
  const ext = path.extname(filePath).toLowerCase();
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
  };
  return mimeTypes[ext] || 'image/jpeg';
};

/**
 * 调用硅基流动AI识别票据信息
 * @param {String} imagePath - 图片文件路径
 * @returns {Promise<Object>} 识别结果
 */
const recognizeTicket = async (imagePath) => {
  try {
    const apiKey = process.env.SILICONFLOW_API_KEY;
    if (!apiKey) {
      throw new AppError('硅基流动API密钥未配置', 500);
    }

    // 将图片转换为base64
    const base64Image = await imageToBase64(imagePath);

    // 构建请求数据
    const requestData = {
      model: SILICONFLOW_MODEL,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `请分析这张票据图片，提取以下信息并以JSON格式返回：
{
  "type": "票据类型(invoice/receipt/check/contract/other)",
  "title": "票据标题或名称",
  "amount": "票据金额(数字)",
  "date": "票据日期(YYYY-MM-DD格式)",
  "description": "票据描述或备注",
  "category": "票据分类",
  "issuer": "开票方或发行方",
  "recipient": "收票方",
  "tax_amount": "税额(如果有)",
  "invoice_number": "发票号码(如果有)"
}

请仔细识别图片中的文字信息，如果某些字段无法识别，请设置为空字符串或null。金额请提取数字部分，日期请转换为标准格式。`
            },
            {
              type: 'image_url',
              image_url: {
                url: base64Image
              }
            }
          ]
        }
      ],
      max_tokens: 1000,
      temperature: 0.1
    };

    // 发送请求到硅基流动API
    const response = await axios.post(SILICONFLOW_API_URL, requestData, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30秒超时
    });

    // 解析响应
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      const content = response.data.choices[0].message.content;
      
      // 尝试解析JSON响应
      try {
        // 提取JSON部分（可能包含在代码块中）
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const jsonStr = jsonMatch[1] || jsonMatch[0];
          const result = JSON.parse(jsonStr);
          
          logger.info('票据识别成功');
          return result;
        } else {
          // 如果没有找到JSON格式，尝试直接解析整个内容
          const result = JSON.parse(content);
          logger.info('票据识别成功');
          return result;
        }
      } catch (parseError) {
        logger.error(`解析AI响应失败: ${parseError.message}`);
        logger.error(`AI响应内容: ${content}`);
        
        // 返回默认结构
        return {
          type: 'other',
          title: '无法识别',
          amount: 0,
          date: new Date().toISOString().split('T')[0],
          description: content,
          category: '',
          issuer: '',
          recipient: '',
          tax_amount: 0,
          invoice_number: ''
        };
      }
    } else {
      throw new AppError('AI识别服务响应异常', 500);
    }
  } catch (error) {
    if (error.response) {
      logger.error(`硅基流动API错误: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
      throw new AppError(`AI识别服务错误: ${error.response.data.error?.message || '未知错误'}`, 500);
    } else if (error.request) {
      logger.error(`网络请求失败: ${error.message}`);
      throw new AppError('AI识别服务网络连接失败', 500);
    } else {
      logger.error(`票据识别失败: ${error.message}`);
      throw error;
    }
  }
};

module.exports = {
  recognizeTicket
};