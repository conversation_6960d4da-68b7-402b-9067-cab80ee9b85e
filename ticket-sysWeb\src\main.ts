import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { Toaster } from 'vue-sonner'
import './style.css'
import App from './App.vue'
import router from './router'
import { useUserStore } from './stores'

// 创建Vue应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 使用Pinia状态管理
app.use(pinia)

// 初始化用户状态
const userStore = useUserStore()
userStore.initUserState()

// 使用路由
app.use(router)

// 注册全局组件
app.component('Toaster', Toaster)

// 挂载应用
app.mount('#app')
