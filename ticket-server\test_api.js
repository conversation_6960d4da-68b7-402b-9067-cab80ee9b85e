/**
 * API接口测试脚本
 */
const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api';

// 测试管理员登录并获取token
async function testAdminLogin() {
  try {
    console.log('测试管理员登录...');
    const response = await axios.post(`${BASE_URL}/admin/login`, {
      username: 'admin',
      password: 'admin123456'
    });
    
    console.log('登录成功:', response.data);
    return response.data.data.token;
  } catch (error) {
    console.error('登录失败:', error.response?.data || error.message);
    return null;
  }
}

// 测试获取用户列表
async function testGetUsers(token) {
  try {
    console.log('\n测试获取用户列表...');
    const response = await axios.get(`${BASE_URL}/admin/users`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('获取用户列表成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('获取用户列表失败:', error.response?.data || error.message);
    return null;
  }
}

// 测试普通用户路由的用户列表
async function testUsersRoute(token) {
  try {
    console.log('\n测试普通用户路由...');
    const response = await axios.get(`${BASE_URL}/users`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('普通用户路由成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('普通用户路由失败:', error.response?.data || error.message);
    return null;
  }
}

// 主测试函数
async function runTests() {
  console.log('开始API接口测试...\n');
  
  // 1. 测试管理员登录
  const token = await testAdminLogin();
  if (!token) {
    console.log('无法获取token，停止测试');
    return;
  }
  
  // 2. 测试管理员用户列表接口
  await testGetUsers(token);
  
  // 3. 测试普通用户路由
  await testUsersRoute(token);
  
  console.log('\n测试完成！');
}

// 运行测试
runTests().catch(console.error);
