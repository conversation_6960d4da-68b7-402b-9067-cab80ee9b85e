# Ticket Server API
作者：程序员王真行
联系微信：jarlib
抖音：程序员王真行

该项目是在2025年7月31日晚间的直播间里用Trae新建的项目。
这是一个基于Express和MySQL的票务系统后端API服务。
该票务系统包含两个客户端（前端）网站，一个是官网客户端网站ticket-web项目，还有一个是后台管理系统网站ticket-sysWeb项目。
该后台服务端系统提供RESTful API接口，供以上前端网站调用。
依赖本地开发环境如下：
- Node.js20
- MySQL5.7

开发机器本地的mysql连接信息如下，大家需要修改db.js以及.env文件，改成自己电脑上的数据库连接信息。
- mysql数据库:ticket
- 数据库用户名:root
- 数据库密码:rootroot

mysql数据库脚本在项目根目录下：ticket.sql
测试账号：
- 管理员用户名:admin
- 管理员密码:admin123456


## 功能特性

- 用户认证与授权（JWT）
- 错误处理与日志记录（Winston）
- 文件上传（Multer）
- 安全头部设置（Helmet）
- 请求数据验证（express-validator）
- 数据库连接（MySQL2）

## 项目结构

```
├── logs/                  # 日志文件目录
├── uploads/               # 上传文件目录
├── src/
│   ├── app.js            # 应用程序入口文件
│   ├── config/           # 配置文件
│   │   └── db.js         # 数据库配置
│   ├── controllers/      # 控制器
│   ├── middlewares/      # 中间件
│   │   ├── auth.js       # 认证中间件
│   │   ├── error.js      # 错误处理中间件
│   │   ├── upload.js     # 文件上传中间件
│   │   ├── validator.js  # 请求验证中间件
│   │   └── validationRules.js # 验证规则
│   ├── routes/           # 路由
│   │   ├── auth.js       # 认证相关路由
│   │   └── users.js      # 用户相关路由
│   └── utils/            # 工具函数
│       ├── jwt.js        # JWT工具
│       └── logger.js     # 日志工具
├── .env                  # 环境变量
├── package.json          # 项目依赖
└── README.md             # 项目说明
```

## 安装与运行

### 前置条件

- Node.js20
- MySQL5.7

### 安装依赖

```bash
npm install
```

### 配置环境变量

复制`.env.example`文件为`.env`，并根据实际情况修改配置：

```
# 服务器配置
PORT=4000
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=your_database

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=1d
```

### 运行开发环境

```bash
npm run dev
```

### 运行生产环境

```bash
npm start
```

## API文档

### 认证相关

#### 登录

```
POST /api/auth/login
```

请求体：

```json
{
  "username": "your_username",
  "password": "your_password"
}
```

#### 注册

```
POST /api/auth/register
```

请求体：

```json
{
  "username": "your_username",
  "password": "your_password",
  "email": "<EMAIL>"
}
```

#### 刷新令牌

```
POST /api/auth/refresh-token
```

请求体：

```json
{
  "token": "your_jwt_token"
}
```

### 用户相关

#### 获取用户列表

```
GET /api/users
```

需要认证。

#### 获取当前用户信息

```
GET /api/users/me
```

需要认证。

#### 获取指定用户信息

```
GET /api/users/:id
```

需要认证。

#### 上传用户头像

```
POST /api/users/avatar
```

需要认证。使用`multipart/form-data`格式，字段名为`avatar`。

## 错误处理

所有API返回的错误格式如下：

```json
{
  "success": false,
  "message": "错误信息"
}
```

在开发环境中，错误响应还会包含错误堆栈信息。

## 日志

日志文件保存在`logs`目录下：

- `info.log`：信息级别日志
- `error.log`：错误级别日志
- `combined.log`：所有级别日志
- `exceptions.log`：未捕获的异常
- `rejections.log`：未处理的Promise拒绝