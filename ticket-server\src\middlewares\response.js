/**
 * 响应处理中间件
 * 确保所有成功的响应状态码都是200
 */

/**
 * 统一响应格式中间件
 * 扩展res对象，添加自定义响应方法
 */
const responseHandler = (req, res, next) => {
  // 成功响应
  res.success = (data, message = '操作成功') => {
    return res.status(200).json({
      status: 'success',
      message,
      data: data || null
    });
  };

  // 创建成功响应
  res.created = (data, message = '创建成功') => {
    return res.status(200).json({
      status: 'success',
      message,
      data: data || null
    });
  };

  // 无内容成功响应
  res.noContent = (message = '操作成功') => {
    return res.status(200).json({
      status: 'success',
      message
    });
  };

  next();
};

module.exports = { responseHandler };