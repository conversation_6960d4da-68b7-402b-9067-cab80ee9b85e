<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTicketStore } from '@/stores/useTicketStore'
import { TicketStatus, type Ticket } from '@/types'
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  Download, 
  Share2, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Calendar, 
  DollarSign, 
  FileText, 
  Tag, 
  User, 
  MapPin,
  Phone,
  Mail,
  Building,
  CreditCard,
  Hash,
  AlertTriangle,
  Shield
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const route = useRoute()
const router = useRouter()
const ticketStore = useTicketStore()

// 当前票据ID
const ticketId = route.params.id as string

// 当前票据数据
const ticket = ref<Ticket | null>(null)

// 加载状态
const loading = ref(true)

// 核对结果显示状态
const showVerificationResult = ref(false)

/**
 * 组件挂载时获取票据详情
 */
onMounted(async () => {
  await fetchTicketDetail()
})

/**
 * 获取票据详情
 */
const fetchTicketDetail = async () => {
  try {
    loading.value = true
    ticket.value = await ticketStore.getTicketById(ticketId)
    if (!ticket.value) {
      toast.error('票据不存在')
      router.push('/tickets')
    }
  } catch (error) {
    toast.error('获取票据详情失败')
    router.push('/tickets')
  } finally {
    loading.value = false
  }
}

/**
 * 删除票据
 */
const deleteTicket = async () => {
  if (!ticket.value) return
  
  if (confirm('确定要删除这个票据吗？此操作不可恢复。')) {
    try {
      await ticketStore.deleteTicket(ticket.value.id)
      toast.success('删除成功')
      router.push('/tickets')
    } catch (error) {
      toast.error('删除失败')
    }
  }
}

/**
 * 编辑票据
 */
const editTicket = () => {
  if (!ticket.value) return
  router.push(`/tickets/${ticket.value.id}/edit`)
}

/**
 * 核对票据
 */
const verifyTicket = async () => {
  if (!ticket.value) return
  
  try {
    const success = await ticketStore.verifyTicket(ticket.value.id, true)
    showVerificationResult.value = true
    
    // 更新票据状态
    if (success) {
      ticket.value.status = TicketStatus.VERIFIED
      toast.success('票据核对通过')
    } else {
      ticket.value.status = TicketStatus.REJECTED
      toast.error('票据核对未通过')
    }
  } catch (error) {
    toast.error('核对失败')
  }
}

/**
 * 导出票据
 */
const exportTicket = () => {
  if (!ticket.value) return
  
  // 模拟导出功能
  toast.success('正在导出票据...')
}

/**
 * 分享票据
 */
const shareTicket = () => {
  if (!ticket.value) return
  
  // 模拟分享功能
  const url = window.location.href
  navigator.clipboard.writeText(url).then(() => {
    toast.success('链接已复制到剪贴板')
  }).catch(() => {
    toast.error('复制失败')
  })
}

/**
 * 格式化金额显示
 * @param amount 金额
 */
const formatAmount = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

/**
 * 格式化日期显示
 * @param date 日期字符串
 */
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

/**
 * 格式化日期时间显示
 * @param date 日期字符串
 */
const formatDateTime = (date: string) => {
  return new Date(date).toLocaleString('zh-CN')
}

/**
 * 获取票据类型文本
 * @param type 类型
 */
const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    invoice: '发票',
    receipt: '收据',
    check: '支票',
    other: '其他'
  }
  return typeMap[type] || '未知'
}

/**
 * 获取票据状态样式
 * @param status 状态
 */
const getStatusClass = (status: string) => {
  switch (status) {
    case 'verified':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

/**
 * 获取票据状态文本
 * @param status 状态
 */
const getStatusText = (status: string) => {
  switch (status) {
    case 'verified':
      return '已验证'
    case 'pending':
      return '待处理'
    case 'rejected':
      return '已拒绝'
    default:
      return '未知'
  }
}

/**
 * 获取状态图标
 * @param status 状态
 */
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'verified':
      return CheckCircle
    case 'pending':
      return Clock
    case 'rejected':
      return XCircle
    default:
      return AlertTriangle
  }
}

// 计算属性
const canVerify = computed(() => {
  return ticket.value && ticket.value.status === TicketStatus.PENDING
})

const verificationResult = computed(() => {
  return ticket.value?.verificationResult
})
</script>

<template>
  <div v-if="loading" class="flex items-center justify-center h-64">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
  
  <div v-else-if="ticket" class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button 
          @click="router.back()"
          class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
        >
          <ArrowLeft class="w-5 h-5" />
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">{{ ticket.title }}</h1>
          <p class="text-gray-600">票据详情</p>
        </div>
      </div>
      
      <div class="flex items-center space-x-2">
        <button 
          v-if="canVerify"
          @click="verifyTicket"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Shield class="w-4 h-4 mr-2" />
          核对票据
        </button>
        <button 
          @click="editTicket"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <Edit class="w-4 h-4 mr-2" />
          编辑
        </button>
        <div class="relative">
          <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors">
            <Share2 class="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>

    <!-- 票据状态卡片 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div 
            :class="[
              'flex items-center px-4 py-2 rounded-lg border',
              getStatusClass(ticket.status)
            ]"
          >
            <component :is="getStatusIcon(ticket.status)" class="w-5 h-5 mr-2" />
            <span class="font-medium">{{ getStatusText(ticket.status) }}</span>
          </div>
          <div class="text-sm text-gray-600">
            <span>创建时间：{{ formatDateTime(ticket.createdAt) }}</span>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <button 
            @click="exportTicket"
            class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            title="导出"
          >
            <Download class="w-5 h-5" />
          </button>
          <button 
            @click="shareTicket"
            class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            title="分享"
          >
            <Share2 class="w-5 h-5" />
          </button>
          <button 
            @click="deleteTicket"
            class="p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-md transition-colors"
            title="删除"
          >
            <Trash2 class="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 主要信息 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 基本信息 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <FileText class="w-5 h-5 text-gray-400" />
                <div>
                  <div class="text-sm text-gray-600">票据类型</div>
                  <div class="font-medium">{{ getTypeText(ticket.type) }}</div>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <DollarSign class="w-5 h-5 text-gray-400" />
                <div>
                  <div class="text-sm text-gray-600">金额</div>
                  <div class="font-medium text-lg text-green-600">{{ formatAmount(ticket.amount) }}</div>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <Calendar class="w-5 h-5 text-gray-400" />
                <div>
                  <div class="text-sm text-gray-600">日期</div>
                  <div class="font-medium">{{ formatDate(ticket.date) }}</div>
                </div>
              </div>
            </div>
            
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <Tag class="w-5 h-5 text-gray-400" />
                <div>
                  <div class="text-sm text-gray-600">分类</div>
                  <div class="font-medium">{{ ticket.category || '未分类' }}</div>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <Hash class="w-5 h-5 text-gray-400" />
                <div>
                  <div class="text-sm text-gray-600">票据编号</div>
                  <div class="font-medium font-mono">{{ ticket.id }}</div>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <User class="w-5 h-5 text-gray-400" />
                <div>
                  <div class="text-sm text-gray-600">创建人</div>
                  <div class="font-medium">{{ ticket.createdBy || '系统用户' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 描述信息 -->
        <div v-if="ticket.description" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">描述信息</h2>
          <div class="prose max-w-none">
            <p class="text-gray-700 whitespace-pre-wrap">{{ ticket.description }}</p>
          </div>
        </div>
        
        <!-- 标签 -->
        <div v-if="ticket.tags && ticket.tags.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">标签</h2>
          <div class="flex flex-wrap gap-2">
            <span 
              v-for="tag in ticket.tags" 
              :key="tag"
              class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
            >
              {{ tag }}
            </span>
          </div>
        </div>
        
        <!-- 附件图片 -->
        <div v-if="ticket.attachments && ticket.attachments.length > 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">附件图片</h2>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div 
              v-for="(attachment, index) in ticket.attachments" 
              :key="index"
              class="relative group"
            >
              <img 
                :src="typeof attachment === 'string' ? attachment : attachment.url" 
                :alt="typeof attachment === 'string' ? `附件 ${index + 1}` : attachment.name"
                class="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-75 transition-opacity"
              >
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all flex items-center justify-center">
                <button class="opacity-0 group-hover:opacity-100 p-2 bg-white rounded-full shadow-md transition-opacity">
                  <Eye class="w-4 h-4 text-gray-600" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 侧边栏信息 -->
      <div class="space-y-6">
        <!-- 核对结果 -->
        <div v-if="verificationResult" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">核对结果</h2>
          <div class="space-y-4">
            <div 
              :class="[
                'flex items-center p-3 rounded-lg',
                verificationResult.isValid 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-red-50 border border-red-200'
              ]"
            >
              <CheckCircle v-if="verificationResult.isValid" class="w-5 h-5 text-green-600 mr-2" />
              <XCircle v-else class="w-5 h-5 text-red-600 mr-2" />
              <span 
                :class="[
                  'font-medium',
                  verificationResult.isValid ? 'text-green-800' : 'text-red-800'
                ]"
              >
                {{ verificationResult.isValid ? '核对通过' : '核对未通过' }}
              </span>
            </div>
            
            <div class="space-y-2">
              <div class="text-sm">
                <span class="text-gray-600">置信度：</span>
                <span class="font-medium">{{ (verificationResult.confidence * 100).toFixed(1) }}%</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-600">核对时间：</span>
                <span class="font-medium">{{ formatDateTime(verificationResult.verifiedAt) }}</span>
              </div>
            </div>
            
            <div v-if="verificationResult.issues && verificationResult.issues.length > 0">
              <div class="text-sm font-medium text-gray-900 mb-2">发现问题：</div>
              <ul class="space-y-1">
                <li 
                  v-for="issue in verificationResult.issues" 
                  :key="issue"
                  class="text-sm text-red-600 flex items-start"
                >
                  <AlertTriangle class="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" />
                  {{ issue }}
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- 操作历史 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">操作历史</h2>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <div class="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-900">票据创建</div>
                <div class="text-xs text-gray-600">{{ formatDateTime(ticket.createdAt) }}</div>
              </div>
            </div>
            
            <div v-if="ticket.updatedAt !== ticket.createdAt" class="flex items-start space-x-3">
              <div class="w-2 h-2 bg-yellow-600 rounded-full mt-2"></div>
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-900">票据更新</div>
                <div class="text-xs text-gray-600">{{ formatDateTime(ticket.updatedAt) }}</div>
              </div>
            </div>
            
            <div v-if="verificationResult" class="flex items-start space-x-3">
              <div 
                :class="[
                  'w-2 h-2 rounded-full mt-2',
                  verificationResult.isValid ? 'bg-green-600' : 'bg-red-600'
                ]"
              ></div>
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-900">
                  {{ verificationResult.isValid ? '核对通过' : '核对失败' }}
                </div>
                <div class="text-xs text-gray-600">{{ formatDateTime(verificationResult.verifiedAt) }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h2>
          <div class="space-y-2">
            <button 
              @click="editTicket"
              class="w-full flex items-center px-3 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
            >
              <Edit class="w-4 h-4 mr-3" />
              编辑票据
            </button>
            <button 
              @click="exportTicket"
              class="w-full flex items-center px-3 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
            >
              <Download class="w-4 h-4 mr-3" />
              导出票据
            </button>
            <button 
              @click="shareTicket"
              class="w-full flex items-center px-3 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
            >
              <Share2 class="w-4 h-4 mr-3" />
              分享票据
            </button>
            <hr class="my-2">
            <button 
              @click="deleteTicket"
              class="w-full flex items-center px-3 py-2 text-left text-red-600 hover:bg-red-50 rounded-md transition-colors"
            >
              <Trash2 class="w-4 h-4 mr-3" />
              删除票据
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div v-else class="text-center py-12">
    <div class="text-gray-400 mb-4">
      <AlertTriangle class="w-16 h-16 mx-auto" />
    </div>
    <h3 class="text-lg font-medium text-gray-900 mb-2">票据不存在</h3>
    <p class="text-gray-600 mb-4">您访问的票据可能已被删除或不存在</p>
    <button 
      @click="router.push('/tickets')"
      class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
    >
      <ArrowLeft class="w-4 h-4 mr-2" />
      返回票据列表
    </button>
  </div>
</template>