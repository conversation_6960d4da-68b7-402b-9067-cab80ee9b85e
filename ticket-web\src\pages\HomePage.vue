<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useTicketStore } from '@/stores/useTicketStore'
import { 
  Plus, 
  FileText, 
  CheckCircle, 
  BarChart3,
  TrendingUp,
  Clock,
  DollarSign,
  AlertCircle
} from 'lucide-vue-next'

const router = useRouter()
const ticketStore = useTicketStore()

/**
 * 组件挂载时获取数据
 */
onMounted(async () => {
  await Promise.all([
    ticketStore.fetchTicketStats(),
    ticketStore.fetchTickets({ page: 1, pageSize: 5 })
  ])
})

/**
 * 快速操作按钮配置
 */
const quickActions = [
  {
    title: '录入票据',
    description: '添加新的票据信息',
    icon: Plus,
    color: 'bg-blue-500 hover:bg-blue-600',
    path: '/create'
  },
  {
    title: '查看列表',
    description: '浏览所有票据',
    icon: FileText,
    color: 'bg-green-500 hover:bg-green-600',
    path: '/tickets'
  },
  {
    title: '票据核对',
    description: '验证票据真伪',
    icon: CheckCircle,
    color: 'bg-purple-500 hover:bg-purple-600',
    path: '/verify'
  },
  {
    title: '统计分析',
    description: '查看数据报表',
    icon: BarChart3,
    color: 'bg-orange-500 hover:bg-orange-600',
    path: '/analytics'
  }
]

/**
 * 最近票据列表（前5条）
 */
const recentTickets = computed(() => {
  return ticketStore.tickets.slice(0, 5)
})

/**
 * 导航到指定页面
 * @param path 路径
 */
const navigateTo = (path: string) => {
  router.push(path)
}

/**
 * 格式化金额显示
 * @param amount 金额
 */
const formatAmount = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

/**
 * 格式化日期显示
 * @param date 日期字符串
 */
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

/**
 * 获取票据状态显示样式
 * @param status 状态
 */
const getStatusClass = (status: string) => {
  switch (status) {
    case 'verified':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

/**
 * 获取票据状态文本
 * @param status 状态
 */
const getStatusText = (status: string) => {
  switch (status) {
    case 'verified':
      return '已验证'
    case 'pending':
      return '待处理'
    case 'rejected':
      return '已拒绝'
    default:
      return '未知'
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900 mb-2">欢迎使用票据助手</h1>
      <p class="text-gray-600">高效管理您的票据信息，让财务工作更简单</p>
    </div>

    <!-- 统计概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 总票据数 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">总票据数</p>
            <p class="text-2xl font-bold text-blue-600">{{ ticketStore.stats?.total || 0 }}</p>
          </div>
          <div class="p-3 bg-blue-100 rounded-full">
            <FileText class="w-6 h-6 text-blue-600" />
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm text-green-600">
          <TrendingUp class="w-4 h-4 mr-1" />
          <span>较上月增长 12%</span>
        </div>
      </div>

      <!-- 本月新增 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">本月新增</p>
            <p class="text-2xl font-bold text-green-600">{{ ticketStore.stats?.verified || 0 }}</p>
          </div>
          <div class="p-3 bg-green-100 rounded-full">
            <Plus class="w-6 h-6 text-green-600" />
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm text-green-600">
          <TrendingUp class="w-4 h-4 mr-1" />
          <span>较上月增长 8%</span>
        </div>
      </div>

      <!-- 待处理数量 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">待处理</p>
            <p class="text-2xl font-bold text-yellow-600">{{ ticketStore.stats?.pending || 0 }}</p>
          </div>
          <div class="p-3 bg-yellow-100 rounded-full">
            <Clock class="w-6 h-6 text-yellow-600" />
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm text-yellow-600">
          <AlertCircle class="w-4 h-4 mr-1" />
          <span>需要及时处理</span>
        </div>
      </div>

      <!-- 总金额 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">总金额</p>
            <p class="text-2xl font-bold text-purple-600">{{ formatAmount(ticketStore.stats?.totalAmount || 0) }}</p>
          </div>
          <div class="p-3 bg-purple-100 rounded-full">
            <DollarSign class="w-6 h-6 text-purple-600" />
          </div>
        </div>
        <div class="mt-4 flex items-center text-sm text-green-600">
          <TrendingUp class="w-4 h-4 mr-1" />
          <span>较上月增长 15%</span>
        </div>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <button
          v-for="action in quickActions"
          :key="action.title"
          @click="navigateTo(action.path)"
          :class="[
            'flex flex-col items-center p-6 rounded-lg text-white transition-all duration-200 transform hover:scale-105 hover:shadow-lg',
            action.color
          ]"
        >
          <component :is="action.icon" class="w-8 h-8 mb-3" />
          <h3 class="font-semibold text-lg mb-1">{{ action.title }}</h3>
          <p class="text-sm opacity-90 text-center">{{ action.description }}</p>
        </button>
      </div>
    </div>

    <!-- 最近票据列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">最近票据</h2>
          <button 
            @click="navigateTo('/tickets')"
            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            查看全部
          </button>
        </div>
      </div>
      
      <div class="divide-y divide-gray-200">
        <div 
          v-if="recentTickets.length === 0"
          class="p-6 text-center text-gray-500"
        >
          <FileText class="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p>暂无票据数据</p>
          <button 
            @click="navigateTo('/create')"
            class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            立即添加
          </button>
        </div>
        
        <div 
          v-for="ticket in recentTickets"
          :key="ticket.id"
          @click="navigateTo(`/tickets/${ticket.id}`)"
          class="p-6 hover:bg-gray-50 cursor-pointer transition-colors duration-200"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3">
                <h3 class="text-sm font-medium text-gray-900">{{ ticket.title }}</h3>
                <span 
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    getStatusClass(ticket.status)
                  ]"
                >
                  {{ getStatusText(ticket.status) }}
                </span>
              </div>
              <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                <span>{{ formatDate(ticket.date) }}</span>
                <span>{{ ticket.category || '未分类' }}</span>
                <span class="font-medium text-gray-900">{{ formatAmount(ticket.amount) }}</span>
              </div>
            </div>
            <div class="flex items-center text-gray-400">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
