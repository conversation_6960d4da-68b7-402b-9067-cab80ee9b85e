# 票据助手网站

一个基于 Vue 3 + TypeScript + Vite 的现代化票据管理系统，帮助用户高效管理各类票据信息。

## 📋 项目简介
官网客户端网站ticket-web项目
票据助手网站是一个功能完整的票据数字化管理平台，主要解决票据管理混乱、查询困难、统计繁琐等问题。系统支持票据录入、查询、核对、统计分析等核心功能，为个人用户和小微企业提供便捷的票据管理服务。

## ✨ 功能特性

### 🏠 首页概览
- 票据统计数据展示（总数、本月新增、待处理、总金额）
- 快速操作入口（录入、查看、核对、统计）
- 最近票据列表展示

### 📝 票据管理
- **票据录入**：支持手动录入和图片上传识别
- **票据列表**：分页展示、多条件筛选、批量操作
- **票据详情**：完整信息展示、编辑、删除功能
- **票据分类**：自定义分类和标签管理

### ✅ 票据核对
- **真伪验证**：票据号码和二维码验证
- **信息比对**：录入信息与原始票据对比
- **批量核对**：支持批量上传和核对
- **核对记录**：完整的核对历史追踪

### 📊 统计分析
- **数据图表**：多维度统计图表展示
- **趋势分析**：票据数量和金额趋势
- **报表导出**：Excel、PDF 格式导出
- **财务分析**：专业的财务数据分析

### ⚙️ 系统设置
- **用户管理**：个人资料和账户设置
- **系统配置**：默认分类、导出格式等
- **权限管理**：普通用户和企业用户权限

## 🛠️ 技术栈

### 前端框架
- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 快速的前端构建工具

### UI 组件
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Lucide Vue Next** - 美观的图标库
- **Vue Sonner** - 优雅的通知组件

### 数据可视化
- **ECharts** - 强大的数据可视化库
- **Vue ECharts** - Vue 3 的 ECharts 组件

### 开发工具
- **Vue Router** - 官方路由管理器
- **VueUse** - Vue 组合式 API 工具集
- **ESLint** - 代码质量检查工具

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 启动开发服务器
```bash
# 使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

访问 [http://localhost:5173](http://localhost:5173) 查看应用。

### 构建生产版本
```bash
# 使用 npm
npm run build

# 或使用 yarn
yarn build
```

### 预览生产版本
```bash
# 使用 npm
npm run preview

# 或使用 yarn
yarn preview
```

## 📁 项目结构

```
src/
├── components/          # 公共组件
│   ├── Empty.vue       # 空状态组件
│   └── Layout.vue      # 布局组件
├── composables/         # 组合式函数
│   └── useTheme.ts     # 主题管理
├── lib/                # 工具库
│   └── utils.ts        # 通用工具函数
├── pages/              # 页面组件
│   ├── HomePage.vue           # 首页
│   ├── CreateTicketPage.vue   # 票据录入页
│   ├── TicketListPage.vue     # 票据列表页
│   ├── TicketDetailPage.vue   # 票据详情页
│   ├── VerificationPage.vue   # 票据核对页
│   ├── StatisticsPage.vue     # 统计分析页
│   └── SettingsPage.vue       # 设置页
├── router/             # 路由配置
│   └── index.ts        # 路由定义
├── services/           # API 服务
│   └── api.ts          # 接口封装
├── stores/             # 状态管理
│   └── useTicketStore.ts # 票据状态管理
├── types/              # 类型定义
│   └── index.ts        # TypeScript 类型
├── App.vue             # 根组件
├── main.ts             # 应用入口
└── style.css           # 全局样式
```

## 🎯 核心功能说明

### 票据类型支持
- 📄 **发票**：增值税发票、普通发票等
- 🧾 **收据**：各类收款收据
- 💳 **支票**：现金支票、转账支票
- 📋 **其他**：自定义票据类型

### 票据状态管理
- ⏳ **待处理**：新录入的票据
- ✅ **已验证**：通过核对的票据
- ❌ **已拒绝**：核对失败的票据

### 数据安全
- 🔒 本地数据存储，保护隐私
- 🛡️ 类型安全的 TypeScript 开发
- 🔍 完整的错误处理机制

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 开源协议

本项目采用 MIT 协议 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

如果您有任何问题或建议，请通过以下方式联系我们：

- 📧 邮箱：<EMAIL>
- 🐛 问题反馈：[GitHub Issues](https://github.com/your-username/ticket-web/issues)
- 💬 讨论交流：[GitHub Discussions](https://github.com/your-username/ticket-web/discussions)

---

⭐ 如果这个项目对您有帮助，请给我们一个 Star！
