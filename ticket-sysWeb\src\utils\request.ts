import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosRequestConfig } from 'axios'
import type { ApiResponse } from '@/types'

// 创建 axios 实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:4000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证 token
    const token = localStorage.getItem('token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 不在拦截器中处理双层嵌套数据结构，保留原始响应
    // 这样可以在页面中根据完整的响应信息处理请求结果
    return response
  },
  (error) => {
    // 处理错误响应
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除 token 并跳转到登录页
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          console.error('权限不足')
          break
        case 404:
          console.error('请求的资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      console.error('网络错误，请检查网络连接')
    } else {
      console.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

/**
 * 通用 GET 请求方法
 * @param url 请求地址
 * @param config 请求配置
 * @returns Promise<T>
 */
export const get = <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return request.get(url, config).then(res => {
    // 直接返回原始响应数据，不处理双层嵌套
    return res.data
  })
}

/**
 * 通用 POST 请求方法
 * @param url 请求地址
 * @param data 请求数据
 * @param config 请求配置
 * @returns Promise<T>
 */
export const post = <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request.post(url, data, config).then(res => {
    // 直接返回原始响应数据，不处理双层嵌套
    return res.data
  })
}

/**
 * 通用 PUT 请求方法
 * @param url 请求地址
 * @param data 请求数据
 * @param config 请求配置
 * @returns Promise<T>
 */
export const put = <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request.put(url, data, config).then(res => {
    // 直接返回原始响应数据，不处理双层嵌套
    return res.data
  })
}

/**
 * 通用 DELETE 请求方法
 * @param url 请求地址
 * @param config 请求配置
 * @returns Promise<T>
 */
export const del = <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return request.delete(url, config).then(res => {
    // 直接返回原始响应数据，不处理双层嵌套
    return res.data
  })
}

export default request