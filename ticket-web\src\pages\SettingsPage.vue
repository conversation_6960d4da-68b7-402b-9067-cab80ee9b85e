<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  User, 
  Settings, 
  Bell, 
  Shield, 
  Database, 
  Download, 
  Upload, 
  Trash2, 
  Save, 
  RefreshCw, 
  Eye, 
  EyeOff,
  Check,
  X,
  AlertTriangle,
  Info,
  Moon,
  Sun,
  Globe,
  Palette,
  Lock,
  Key,
  Mail,
  Phone,
  Building,
  Calendar
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const router = useRouter()

// 当前激活的设置标签
const activeTab = ref('profile')

// 用户资料设置
const profileSettings = reactive({
  name: '张三',
  email: 'zhang<PERSON>@example.com',
  phone: '13800138000',
  department: '财务部',
  position: '会计',
  avatar: '',
  bio: '负责公司票据管理和财务核算工作'
})

// 系统偏好设置
const systemSettings = reactive({
  theme: 'light', // light, dark, auto
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  currency: 'CNY',
  pageSize: 20,
  autoSave: true,
  showTutorial: true
})

// 通知设置
const notificationSettings = reactive({
  emailNotifications: true,
  pushNotifications: true,
  smsNotifications: false,
  notifyOnVerification: true,
  notifyOnRejection: true,
  notifyOnExpiry: true,
  dailyReport: true,
  weeklyReport: false,
  monthlyReport: true
})

// 安全设置
const securitySettings = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
  twoFactorEnabled: false,
  sessionTimeout: 30, // 分钟
  loginNotifications: true,
  allowMultipleSessions: false,
  passwordExpiry: 90 // 天
})

// 数据管理设置
const dataSettings = reactive({
  autoBackup: true,
  backupFrequency: 'daily', // daily, weekly, monthly
  retentionPeriod: 365, // 天
  exportFormat: 'excel', // excel, csv, pdf
  compressionEnabled: true,
  encryptionEnabled: true
})

// 核对设置
const verificationSettings = reactive({
  autoVerify: true,
  strictMode: false,
  confidenceThreshold: 0.8,
  enableOCR: true,
  enableImageAnalysis: true,
  batchSize: 10,
  retryAttempts: 3,
  timeoutSeconds: 30
})

// 显示密码
const showPasswords = reactive({
  current: false,
  new: false,
  confirm: false
})

// 保存状态
const saving = ref(false)

// 设置标签
const settingTabs = [
  { id: 'profile', name: '个人资料', icon: User },
  { id: 'system', name: '系统偏好', icon: Settings },
  { id: 'notifications', name: '通知设置', icon: Bell },
  { id: 'security', name: '安全设置', icon: Shield },
  { id: 'data', name: '数据管理', icon: Database },
  { id: 'verification', name: '核对设置', icon: Check }
]

/**
 * 组件挂载时加载设置
 */
onMounted(() => {
  loadSettings()
})

/**
 * 加载设置数据
 */
const loadSettings = async () => {
  try {
    // 模拟从服务器加载设置
    await new Promise(resolve => setTimeout(resolve, 500))
    toast.success('设置加载完成')
  } catch (error) {
    toast.error('加载设置失败')
  }
}

/**
 * 保存个人资料
 */
const saveProfile = async () => {
  try {
    saving.value = true
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    toast.success('个人资料保存成功')
  } catch (error) {
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 保存系统设置
 */
const saveSystemSettings = async () => {
  try {
    saving.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    toast.success('系统设置保存成功')
  } catch (error) {
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 保存通知设置
 */
const saveNotificationSettings = async () => {
  try {
    saving.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    toast.success('通知设置保存成功')
  } catch (error) {
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 修改密码
 */
const changePassword = async () => {
  if (!securitySettings.currentPassword) {
    toast.error('请输入当前密码')
    return
  }
  
  if (!securitySettings.newPassword) {
    toast.error('请输入新密码')
    return
  }
  
  if (securitySettings.newPassword !== securitySettings.confirmPassword) {
    toast.error('两次输入的密码不一致')
    return
  }
  
  if (securitySettings.newPassword.length < 8) {
    toast.error('密码长度至少8位')
    return
  }
  
  try {
    saving.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 清空密码字段
    securitySettings.currentPassword = ''
    securitySettings.newPassword = ''
    securitySettings.confirmPassword = ''
    
    toast.success('密码修改成功')
  } catch (error) {
    toast.error('密码修改失败')
  } finally {
    saving.value = false
  }
}

/**
 * 保存安全设置
 */
const saveSecuritySettings = async () => {
  try {
    saving.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    toast.success('安全设置保存成功')
  } catch (error) {
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 保存数据设置
 */
const saveDataSettings = async () => {
  try {
    saving.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    toast.success('数据设置保存成功')
  } catch (error) {
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 保存核对设置
 */
const saveVerificationSettings = async () => {
  try {
    saving.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    toast.success('核对设置保存成功')
  } catch (error) {
    toast.error('保存失败')
  } finally {
    saving.value = false
  }
}

/**
 * 导出数据
 */
const exportData = () => {
  toast.success('正在导出数据...')
}

/**
 * 导入数据
 */
const importData = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json,.csv,.xlsx'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      toast.success(`正在导入文件: ${file.name}`)
    }
  }
  input.click()
}

/**
 * 清除缓存
 */
const clearCache = () => {
  if (confirm('确定要清除所有缓存数据吗？这将清除本地存储的临时数据。')) {
    localStorage.clear()
    sessionStorage.clear()
    toast.success('缓存已清除')
  }
}

/**
 * 重置设置
 */
const resetSettings = () => {
  if (confirm('确定要重置所有设置到默认值吗？此操作不可恢复。')) {
    // 重置到默认值
    Object.assign(systemSettings, {
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      currency: 'CNY',
      pageSize: 20,
      autoSave: true,
      showTutorial: true
    })
    
    toast.success('设置已重置')
  }
}

/**
 * 上传头像
 */
const uploadAvatar = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        profileSettings.avatar = e.target?.result as string
        toast.success('头像上传成功')
      }
      reader.readAsDataURL(file)
    }
  }
  input.click()
}

/**
 * 切换密码显示
 * @param field 密码字段
 */
const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
  showPasswords[field] = !showPasswords[field]
}

/**
 * 获取主题图标
 */
const getThemeIcon = (theme: string) => {
  switch (theme) {
    case 'light':
      return Sun
    case 'dark':
      return Moon
    default:
      return Settings
  }
}
</script>

<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">系统设置</h1>
        <p class="text-gray-600">管理您的账户和系统偏好设置</p>
      </div>
    </div>

    <div class="flex flex-col lg:flex-row gap-6">
      <!-- 设置导航 -->
      <div class="lg:w-64">
        <nav class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <ul class="space-y-1">
            <li v-for="tab in settingTabs" :key="tab.id">
              <button 
                @click="activeTab = tab.id"
                :class="[
                  'w-full flex items-center px-3 py-2 text-left rounded-md transition-colors',
                  activeTab === tab.id 
                    ? 'bg-blue-100 text-blue-700 border-blue-200' 
                    : 'text-gray-700 hover:bg-gray-100'
                ]"
              >
                <component :is="tab.icon" class="w-4 h-4 mr-3" />
                {{ tab.name }}
              </button>
            </li>
          </ul>
        </nav>
      </div>

      <!-- 设置内容 -->
      <div class="flex-1">
        <!-- 个人资料 -->
        <div v-if="activeTab === 'profile'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">个人资料</h2>
            <button 
              @click="saveProfile"
              :disabled="saving"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Save v-if="!saving" class="w-4 h-4 mr-2" />
              <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
          
          <div class="space-y-6">
            <!-- 头像 -->
            <div class="flex items-center space-x-4">
              <div class="relative">
                <div class="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
                  <img 
                    v-if="profileSettings.avatar" 
                    :src="profileSettings.avatar" 
                    alt="头像" 
                    class="w-full h-full object-cover"
                  >
                  <User v-else class="w-8 h-8 text-gray-400" />
                </div>
                <button 
                  @click="uploadAvatar"
                  class="absolute bottom-0 right-0 p-1 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
                >
                  <Upload class="w-3 h-3" />
                </button>
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900">头像</div>
                <div class="text-sm text-gray-600">点击右下角按钮上传新头像</div>
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                <input 
                  v-model="profileSettings.name"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                <input 
                  v-model="profileSettings.email"
                  type="email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">电话</label>
                <input 
                  v-model="profileSettings.phone"
                  type="tel"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                <input 
                  v-model="profileSettings.department"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">职位</label>
                <input 
                  v-model="profileSettings.position"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">个人简介</label>
              <textarea 
                v-model="profileSettings.bio"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="简单介绍一下自己..."
              ></textarea>
            </div>
          </div>
        </div>

        <!-- 系统偏好 -->
        <div v-else-if="activeTab === 'system'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">系统偏好</h2>
            <button 
              @click="saveSystemSettings"
              :disabled="saving"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Save v-if="!saving" class="w-4 h-4 mr-2" />
              <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
          
          <div class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">主题</label>
                <select 
                  v-model="systemSettings.theme"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="light">浅色主题</option>
                  <option value="dark">深色主题</option>
                  <option value="auto">跟随系统</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">语言</label>
                <select 
                  v-model="systemSettings.language"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="zh-CN">简体中文</option>
                  <option value="zh-TW">繁体中文</option>
                  <option value="en-US">English</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">时区</label>
                <select 
                  v-model="systemSettings.timezone"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Asia/Shanghai">北京时间 (UTC+8)</option>
                  <option value="Asia/Hong_Kong">香港时间 (UTC+8)</option>
                  <option value="Asia/Taipei">台北时间 (UTC+8)</option>
                  <option value="UTC">协调世界时 (UTC)</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">日期格式</label>
                <select 
                  v-model="systemSettings.dateFormat"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="YYYY-MM-DD">2024-01-01</option>
                  <option value="DD/MM/YYYY">01/01/2024</option>
                  <option value="MM/DD/YYYY">01/01/2024</option>
                  <option value="DD-MM-YYYY">01-01-2024</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">货币</label>
                <select 
                  v-model="systemSettings.currency"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="CNY">人民币 (¥)</option>
                  <option value="USD">美元 ($)</option>
                  <option value="EUR">欧元 (€)</option>
                  <option value="HKD">港币 (HK$)</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">每页显示数量</label>
                <select 
                  v-model.number="systemSettings.pageSize"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option :value="10">10</option>
                  <option :value="20">20</option>
                  <option :value="50">50</option>
                  <option :value="100">100</option>
                </select>
              </div>
            </div>
            
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium text-gray-900">自动保存</div>
                  <div class="text-sm text-gray-600">编辑时自动保存草稿</div>
                </div>
                <input 
                  v-model="systemSettings.autoSave"
                  type="checkbox" 
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
              </div>
              
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium text-gray-900">显示教程</div>
                  <div class="text-sm text-gray-600">为新功能显示引导教程</div>
                </div>
                <input 
                  v-model="systemSettings.showTutorial"
                  type="checkbox" 
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
              </div>
            </div>
          </div>
        </div>

        <!-- 通知设置 -->
        <div v-else-if="activeTab === 'notifications'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">通知设置</h2>
            <button 
              @click="saveNotificationSettings"
              :disabled="saving"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Save v-if="!saving" class="w-4 h-4 mr-2" />
              <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
          
          <div class="space-y-6">
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">通知方式</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <Mail class="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <div class="text-sm font-medium text-gray-900">邮件通知</div>
                      <div class="text-sm text-gray-600">通过邮件接收重要通知</div>
                    </div>
                  </div>
                  <input 
                    v-model="notificationSettings.emailNotifications"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <Bell class="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <div class="text-sm font-medium text-gray-900">推送通知</div>
                      <div class="text-sm text-gray-600">浏览器推送通知</div>
                    </div>
                  </div>
                  <input 
                    v-model="notificationSettings.pushNotifications"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <Phone class="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <div class="text-sm font-medium text-gray-900">短信通知</div>
                      <div class="text-sm text-gray-600">重要事件短信提醒</div>
                    </div>
                  </div>
                  <input 
                    v-model="notificationSettings.smsNotifications"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
              </div>
            </div>
            
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">通知内容</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">核对通过通知</div>
                    <div class="text-sm text-gray-600">票据核对通过时通知</div>
                  </div>
                  <input 
                    v-model="notificationSettings.notifyOnVerification"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">核对拒绝通知</div>
                    <div class="text-sm text-gray-600">票据核对被拒绝时通知</div>
                  </div>
                  <input 
                    v-model="notificationSettings.notifyOnRejection"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">到期提醒</div>
                    <div class="text-sm text-gray-600">票据即将到期时提醒</div>
                  </div>
                  <input 
                    v-model="notificationSettings.notifyOnExpiry"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
              </div>
            </div>
            
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">定期报告</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">日报</div>
                    <div class="text-sm text-gray-600">每日票据处理汇总</div>
                  </div>
                  <input 
                    v-model="notificationSettings.dailyReport"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">周报</div>
                    <div class="text-sm text-gray-600">每周票据统计报告</div>
                  </div>
                  <input 
                    v-model="notificationSettings.weeklyReport"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">月报</div>
                    <div class="text-sm text-gray-600">每月票据分析报告</div>
                  </div>
                  <input 
                    v-model="notificationSettings.monthlyReport"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 安全设置 -->
        <div v-else-if="activeTab === 'security'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">安全设置</h2>
          </div>
          
          <div class="space-y-8">
            <!-- 修改密码 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">修改密码</h3>
              <div class="space-y-4 max-w-md">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">当前密码</label>
                  <div class="relative">
                    <input 
                      v-model="securitySettings.currentPassword"
                      :type="showPasswords.current ? 'text' : 'password'"
                      class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                    <button 
                      @click="togglePasswordVisibility('current')"
                      type="button"
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <Eye v-if="showPasswords.current" class="w-4 h-4 text-gray-400" />
                      <EyeOff v-else class="w-4 h-4 text-gray-400" />
                    </button>
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                  <div class="relative">
                    <input 
                      v-model="securitySettings.newPassword"
                      :type="showPasswords.new ? 'text' : 'password'"
                      class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                    <button 
                      @click="togglePasswordVisibility('new')"
                      type="button"
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <Eye v-if="showPasswords.new" class="w-4 h-4 text-gray-400" />
                      <EyeOff v-else class="w-4 h-4 text-gray-400" />
                    </button>
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">确认新密码</label>
                  <div class="relative">
                    <input 
                      v-model="securitySettings.confirmPassword"
                      :type="showPasswords.confirm ? 'text' : 'password'"
                      class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                    <button 
                      @click="togglePasswordVisibility('confirm')"
                      type="button"
                      class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      <Eye v-if="showPasswords.confirm" class="w-4 h-4 text-gray-400" />
                      <EyeOff v-else class="w-4 h-4 text-gray-400" />
                    </button>
                  </div>
                </div>
                
                <button 
                  @click="changePassword"
                  :disabled="saving"
                  class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  <Key v-if="!saving" class="w-4 h-4 mr-2" />
                  <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
                  {{ saving ? '修改中...' : '修改密码' }}
                </button>
              </div>
            </div>
            
            <!-- 安全选项 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">安全选项</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">双因素认证</div>
                    <div class="text-sm text-gray-600">使用手机验证码增强账户安全</div>
                  </div>
                  <input 
                    v-model="securitySettings.twoFactorEnabled"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">登录通知</div>
                    <div class="text-sm text-gray-600">新设备登录时发送通知</div>
                  </div>
                  <input 
                    v-model="securitySettings.loginNotifications"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">允许多设备登录</div>
                    <div class="text-sm text-gray-600">允许同时在多个设备上登录</div>
                  </div>
                  <input 
                    v-model="securitySettings.allowMultipleSessions"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
              </div>
            </div>
            
            <!-- 会话设置 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">会话设置</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">会话超时 (分钟)</label>
                  <select 
                    v-model.number="securitySettings.sessionTimeout"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option :value="15">15 分钟</option>
                    <option :value="30">30 分钟</option>
                    <option :value="60">1 小时</option>
                    <option :value="240">4 小时</option>
                    <option :value="480">8 小时</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">密码有效期 (天)</label>
                  <select 
                    v-model.number="securitySettings.passwordExpiry"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option :value="30">30 天</option>
                    <option :value="60">60 天</option>
                    <option :value="90">90 天</option>
                    <option :value="180">180 天</option>
                    <option :value="365">365 天</option>
                  </select>
                </div>
              </div>
            </div>
            
            <button 
              @click="saveSecuritySettings"
              :disabled="saving"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Save v-if="!saving" class="w-4 h-4 mr-2" />
              <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
              {{ saving ? '保存中...' : '保存设置' }}
            </button>
          </div>
        </div>

        <!-- 数据管理 -->
        <div v-else-if="activeTab === 'data'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">数据管理</h2>
          </div>
          
          <div class="space-y-8">
            <!-- 备份设置 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">自动备份</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">启用自动备份</div>
                    <div class="text-sm text-gray-600">定期自动备份数据</div>
                  </div>
                  <input 
                    v-model="dataSettings.autoBackup"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">备份频率</label>
                    <select 
                      v-model="dataSettings.backupFrequency"
                      :disabled="!dataSettings.autoBackup"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
                    >
                      <option value="daily">每日</option>
                      <option value="weekly">每周</option>
                      <option value="monthly">每月</option>
                    </select>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">保留期限 (天)</label>
                    <input 
                      v-model.number="dataSettings.retentionPeriod"
                      type="number"
                      min="30"
                      max="3650"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 导入导出 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">数据导入导出</h3>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">导出格式</label>
                  <select 
                    v-model="dataSettings.exportFormat"
                    class="w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="excel">Excel (.xlsx)</option>
                    <option value="csv">CSV (.csv)</option>
                    <option value="pdf">PDF (.pdf)</option>
                    <option value="json">JSON (.json)</option>
                  </select>
                </div>
                
                <div class="flex space-x-4">
                  <button 
                    @click="exportData"
                    class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    <Download class="w-4 h-4 mr-2" />
                    导出数据
                  </button>
                  
                  <button 
                    @click="importData"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    <Upload class="w-4 h-4 mr-2" />
                    导入数据
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 数据安全 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">数据安全</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">启用压缩</div>
                    <div class="text-sm text-gray-600">压缩备份文件以节省空间</div>
                  </div>
                  <input 
                    v-model="dataSettings.compressionEnabled"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">启用加密</div>
                    <div class="text-sm text-gray-600">加密敏感数据</div>
                  </div>
                  <input 
                    v-model="dataSettings.encryptionEnabled"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
              </div>
            </div>
            
            <!-- 危险操作 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">危险操作</h3>
              <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex items-start">
                  <AlertTriangle class="w-5 h-5 text-red-600 mr-3 mt-0.5" />
                  <div class="flex-1">
                    <div class="text-sm font-medium text-red-800 mb-2">清除缓存数据</div>
                    <div class="text-sm text-red-700 mb-3">这将清除所有本地缓存数据，包括临时文件和用户偏好设置。</div>
                    <button 
                      @click="clearCache"
                      class="inline-flex items-center px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
                    >
                      <Trash2 class="w-4 h-4 mr-1" />
                      清除缓存
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <button 
              @click="saveDataSettings"
              :disabled="saving"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Save v-if="!saving" class="w-4 h-4 mr-2" />
              <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
              {{ saving ? '保存中...' : '保存设置' }}
            </button>
          </div>
        </div>

        <!-- 核对设置 -->
        <div v-else-if="activeTab === 'verification'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900">核对设置</h2>
            <button 
              @click="saveVerificationSettings"
              :disabled="saving"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Save v-if="!saving" class="w-4 h-4 mr-2" />
              <RefreshCw v-else class="w-4 h-4 mr-2 animate-spin" />
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
          
          <div class="space-y-6">
            <!-- 核对选项 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">核对选项</h3>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">自动核对</div>
                    <div class="text-sm text-gray-600">新票据自动进行核对</div>
                  </div>
                  <input 
                    v-model="verificationSettings.autoVerify"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">严格模式</div>
                    <div class="text-sm text-gray-600">使用更严格的核对标准</div>
                  </div>
                  <input 
                    v-model="verificationSettings.strictMode"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">启用OCR识别</div>
                    <div class="text-sm text-gray-600">自动识别票据文字信息</div>
                  </div>
                  <input 
                    v-model="verificationSettings.enableOCR"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
                
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-sm font-medium text-gray-900">启用图像分析</div>
                    <div class="text-sm text-gray-600">分析票据图像质量和完整性</div>
                  </div>
                  <input 
                    v-model="verificationSettings.enableImageAnalysis"
                    type="checkbox" 
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  >
                </div>
              </div>
            </div>
            
            <!-- 核对参数 -->
            <div>
              <h3 class="text-md font-medium text-gray-900 mb-4">核对参数</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">置信度阈值</label>
                  <div class="space-y-2">
                    <input 
                      v-model.number="verificationSettings.confidenceThreshold"
                      type="range" 
                      min="0.5" 
                      max="1" 
                      step="0.05"
                      class="w-full"
                    >
                    <div class="text-sm text-gray-600">
                      {{ (verificationSettings.confidenceThreshold * 100).toFixed(0) }}%
                    </div>
                  </div>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">批量处理大小</label>
                  <select 
                    v-model.number="verificationSettings.batchSize"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option :value="5">5</option>
                    <option :value="10">10</option>
                    <option :value="20">20</option>
                    <option :value="50">50</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">重试次数</label>
                  <select 
                    v-model.number="verificationSettings.retryAttempts"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option :value="1">1 次</option>
                    <option :value="2">2 次</option>
                    <option :value="3">3 次</option>
                    <option :value="5">5 次</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">超时时间 (秒)</label>
                  <select 
                    v-model.number="verificationSettings.timeoutSeconds"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option :value="15">15 秒</option>
                    <option :value="30">30 秒</option>
                    <option :value="60">60 秒</option>
                    <option :value="120">120 秒</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>