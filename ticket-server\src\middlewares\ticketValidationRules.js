/**
 * 票据验证规则
 * 定义票据相关请求的验证规则
 */
const { body, param } = require('express-validator');

/**
 * 票据上传验证规则
 */
const ticketUploadRules = [
  body('type')
    .optional()
    .isIn(['invoice', 'receipt', 'check', 'contract', 'other'])
    .withMessage('票据类型必须是：invoice/receipt/check/contract/other'),
  body('category')
    .optional()
    .isLength({ max: 50 })
    .withMessage('票据分类长度不能超过50个字符')
];

/**
 * 票据ID参数验证规则
 */
const ticketIdParamRules = [
  param('ticket_id')
    .notEmpty().withMessage('票据ID不能为空')
    .isUUID().withMessage('票据ID必须是有效的UUID格式')
];

module.exports = {
  ticketUploadRules,
  ticketIdParamRules
};