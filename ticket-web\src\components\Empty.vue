<script setup lang="ts">
import { FileX, Plus, Search } from 'lucide-vue-next'

/**
 * 空状态组件属性接口
 */
interface Props {
  /** 空状态类型 */
  type?: 'no-data' | 'no-search' | 'no-tickets' | 'no-results'
  /** 自定义标题 */
  title?: string
  /** 自定义描述 */
  description?: string
  /** 是否显示操作按钮 */
  showAction?: boolean
  /** 操作按钮文本 */
  actionText?: string
  /** 操作按钮点击事件 */
  onAction?: () => void
}

const props = withDefaults(defineProps<Props>(), {
  type: 'no-data',
  showAction: false,
  actionText: '立即创建'
})

const emit = defineEmits<{
  action: []
}>()

/**
 * 获取空状态配置
 */
const getEmptyConfig = () => {
  switch (props.type) {
    case 'no-tickets':
      return {
        icon: FileX,
        title: '暂无票据',
        description: '您还没有创建任何票据，点击下方按钮开始创建您的第一张票据',
        actionText: '创建票据'
      }
    case 'no-search':
      return {
        icon: Search,
        title: '未找到相关内容',
        description: '请尝试调整搜索条件或关键词',
        actionText: '重新搜索'
      }
    case 'no-results':
      return {
        icon: Search,
        title: '没有符合条件的结果',
        description: '请尝试修改筛选条件或搜索关键词',
        actionText: '清除筛选'
      }
    default:
      return {
        icon: FileX,
        title: '暂无数据',
        description: '当前没有可显示的内容',
        actionText: '刷新页面'
      }
  }
}

const config = getEmptyConfig()

/**
 * 处理操作按钮点击
 */
const handleAction = () => {
  if (props.onAction) {
    props.onAction()
  } else {
    emit('action')
  }
}
</script>

<template>
  <div class="flex flex-col items-center justify-center py-16 px-4 text-center">
    <!-- 图标 -->
    <div class="w-16 h-16 mb-6 text-gray-300">
      <component :is="config.icon" class="w-full h-full" />
    </div>
    
    <!-- 标题 -->
    <h3 class="text-lg font-medium text-gray-900 mb-2">
      {{ title || config.title }}
    </h3>
    
    <!-- 描述 -->
    <p class="text-sm text-gray-500 mb-6 max-w-sm leading-relaxed">
      {{ description || config.description }}
    </p>
    
    <!-- 操作按钮 -->
    <button
      v-if="showAction"
      @click="handleAction"
      class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
    >
      <Plus class="w-4 h-4 mr-2" />
      {{ actionText || config.actionText }}
    </button>
  </div>
</template>
