<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useTicketStore } from '@/stores/useTicketStore'
import { TicketType, TicketStatus } from '@/types'
import { 
  Upload, 
  X, 
  Save, 
  ArrowLeft,
  Camera,
  FileText,
  Tag,
  Calendar,
  DollarSign
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'


const router = useRouter()
const ticketStore = useTicketStore()

// 表单数据
const formData = reactive({
  type: TicketType.INVOICE,
  title: '',
  amount: 0,
  date: new Date().toISOString().split('T')[0],
  description: '',
  category: '',
  tags: [] as string[],
  imageUrl: ''
})

// 表单状态
const loading = ref(false)
const imagePreview = ref('')
const tagInput = ref('')

// 票据类型选项
const ticketTypes = [
  { value: TicketType.INVOICE, label: '发票' },
  { value: TicketType.RECEIPT, label: '收据' },
  { value: TicketType.CHECK, label: '支票' },
  { value: TicketType.OTHER, label: '其他' }
]

// 常用分类
const commonCategories = [
  '办公用品', '餐饮', '交通', '住宿', '通讯', '水电费', '租金', '维修', '培训', '其他'
]

/**
 * 处理图片上传
 * @param event 文件选择事件
 */
const handleImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      toast.error('请选择图片文件')
      return
    }
    
    // 验证文件大小（5MB）
    if (file.size > 5 * 1024 * 1024) {
      toast.error('图片大小不能超过5MB')
      return
    }
    
    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
      imagePreview.value = e.target?.result as string
      formData.imageUrl = e.target?.result as string
    }
    reader.readAsDataURL(file)
    
    // 上传图片到后端API进行识别
    await uploadTicketImage(file)
  }
}

/**
 * 上传票据图片到后端API进行识别
 * @param file 图片文件
 */
const uploadTicketImage = async (file: File) => {
  console.log('🔍 开始上传票据图片，文件信息:', {
    name: file.name,
    size: file.size,
    type: file.type
  })
  
  toast.info('正在上传和识别票据信息，请稍候...')
  loading.value = true
  
  try {
    // 创建FormData对象
    const formDataUpload = new FormData()
    formDataUpload.append('image', file)
    
    console.log('📤 正在上传图片到API...')
    
    // 发送POST请求到后端API
    const response = await fetch('http://localhost:4000/api/tickets/upload', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.ANNSwya0TVLgqN3j971zhsu1ttK8jUrsBo9SbzSTNxQ'
      },
      body: formDataUpload
    })
    
    console.log('📡 API响应状态:', response.status)
    
    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`)
    }
    
    const result = await response.json()
    console.log('✅ API响应成功，完整结果:', result)
    
    // 检查响应格式
    if (result.status !== 'success') {
      throw new Error(result.message || '上传失败')
    }
    
    // 处理双层嵌套的data结构
    const ticketData = result.data?.data?.ticket || result.data?.ticket
    const recognitionResult = result.data?.data?.recognition_result || result.data?.recognition_result
    const fileInfo = result.data?.data?.file_info || result.data?.file_info
    
    console.log('🎫 票据数据:', ticketData)
    console.log('🔍 识别结果:', recognitionResult)
    console.log('📁 文件信息:', fileInfo)
    
    // 填充表单数据
    if (ticketData) {
      fillFormFromApiResponse(ticketData, recognitionResult)
    }
    
    toast.success('票据上传和识别成功！')
    
  } catch (error) {
    console.error('❌ 票据上传失败:', error)
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack
    })
    toast.error(`票据上传失败: ${error.message}`)
  } finally {
    loading.value = false
    console.log('🏁 票据上传流程结束')
  }
}

/**
 * 根据API响应填充表单数据
 * @param ticketData 票据数据
 * @param recognitionResult 识别结果
 */
const fillFormFromApiResponse = (ticketData: any, recognitionResult?: any) => {
  console.log('🔍 开始填充表单数据')
  console.log('🎫 票据数据:', ticketData)
  console.log('🔍 识别结果:', recognitionResult)
  
  // 记录表单填充前的状态
  console.log('📝 表单填充前状态:', {
    title: formData.title,
    amount: formData.amount,
    date: formData.date,
    category: formData.category,
    type: formData.type
  })
  
  // 填充基本信息
  if (ticketData.title && !formData.title) {
    formData.title = ticketData.title
    console.log('✅ 标题已填充:', ticketData.title)
  }
  
  if (ticketData.amount && ticketData.amount > 0 && !formData.amount) {
    formData.amount = ticketData.amount
    console.log('✅ 金额已填充:', ticketData.amount)
  }
  
  if (ticketData.date && !formData.date) {
     // 处理日期格式，将ISO格式转换为YYYY-MM-DD格式
     const dateValue = new Date(ticketData.date)
     if (!isNaN(dateValue.getTime())) {
       const formattedDate = dateValue.toISOString().split('T')[0]
       formData.date = formattedDate
       console.log('✅ 日期已填充:', formattedDate)
     } else {
       console.warn('⚠️ 日期格式无效:', ticketData.date)
     }
   }
  
  if (ticketData.category && !formData.category) {
    formData.category = ticketData.category
    console.log('✅ 分类已填充:', ticketData.category)
  }
  
  if (ticketData.type && !formData.type) {
    formData.type = ticketData.type
    console.log('✅ 类型已填充:', ticketData.type)
  }
  
  if (ticketData.description && !formData.description) {
    formData.description = ticketData.description
    console.log('✅ 描述已填充:', ticketData.description)
  }
  
  // 记录表单填充后的状态
  console.log('📝 表单填充后状态:', {
    title: formData.title,
    amount: formData.amount,
    date: formData.date,
    category: formData.category,
    type: formData.type
  })
  
  console.log('🎉 表单数据填充完成')
}



/**
 * 移除图片
 */
const removeImage = () => {
  imagePreview.value = ''
  formData.imageUrl = ''
}

/**
 * 添加标签
 */
const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !formData.tags.includes(tag)) {
    formData.tags.push(tag)
    tagInput.value = ''
  }
}

/**
 * 移除标签
 * @param index 标签索引
 */
const removeTag = (index: number) => {
  formData.tags.splice(index, 1)
}

/**
 * 处理标签输入回车事件
 * @param event 键盘事件
 */
const handleTagKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    addTag()
  }
}

/**
 * 验证表单数据
 */
const validateForm = () => {
  if (!formData.title.trim()) {
    toast.error('请输入票据标题')
    return false
  }
  
  if (formData.amount <= 0) {
    toast.error('请输入有效的金额')
    return false
  }
  
  if (!formData.date) {
    toast.error('请选择日期')
    return false
  }
  
  return true
}

/**
 * 提交表单
 */
const submitForm = async () => {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    await ticketStore.createTicket({
      ...formData,
      status: TicketStatus.PENDING
    })
    
    toast.success('票据创建成功')
    router.push('/tickets')
  } catch (error) {
    console.error('创建票据失败:', error)
    toast.error('创建票据失败，请重试')
  } finally {
    loading.value = false
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}
</script>

<template>
  <div class="max-w-4xl mx-auto">
    <!-- 页面头部 -->
    <div class="mb-6">
      <div class="flex items-center space-x-4 mb-4">
        <button 
          @click="goBack"
          class="p-2 rounded-md hover:bg-gray-100 transition-colors"
        >
          <ArrowLeft class="w-5 h-5" />
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">录入票据</h1>
          <p class="text-gray-600">添加新的票据信息</p>
        </div>
      </div>
    </div>

    <form @submit.prevent="submitForm" class="space-y-6">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 左侧：基本信息 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 基本信息卡片 -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
              <FileText class="w-5 h-5 text-blue-600 mr-2" />
              <h2 class="text-lg font-semibold text-gray-900">基本信息</h2>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 票据类型 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  票据类型 <span class="text-red-500">*</span>
                </label>
                <select 
                  v-model="formData.type"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option v-for="type in ticketTypes" :key="type.value" :value="type.value">
                    {{ type.label }}
                  </option>
                </select>
              </div>
              
              <!-- 票据标题 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  票据标题 <span class="text-red-500">*</span>
                </label>
                <input 
                  v-model="formData.title"
                  type="text" 
                  placeholder="请输入票据标题"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
              </div>
              
              <!-- 金额 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  金额 <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign class="h-4 w-4 text-gray-400" />
                  </div>
                  <input 
                    v-model.number="formData.amount"
                    type="number" 
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                </div>
              </div>
              
              <!-- 日期 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  日期 <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar class="h-4 w-4 text-gray-400" />
                  </div>
                  <input 
                    v-model="formData.date"
                    type="date"
                    class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                </div>
              </div>
            </div>
            
            <!-- 分类 -->
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                分类
              </label>
              <div class="flex flex-wrap gap-2 mb-2">
                <button
                  v-for="category in commonCategories"
                  :key="category"
                  type="button"
                  @click="formData.category = category"
                  :class="[
                    'px-3 py-1 text-sm rounded-full border transition-colors',
                    formData.category === category
                      ? 'bg-blue-100 text-blue-700 border-blue-300'
                      : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                  ]"
                >
                  {{ category }}
                </button>
              </div>
              <input 
                v-model="formData.category"
                type="text" 
                placeholder="或输入自定义分类"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
            </div>
            
            <!-- 描述 -->
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                描述
              </label>
              <textarea 
                v-model="formData.description"
                rows="3"
                placeholder="请输入票据描述（可选）"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              ></textarea>
            </div>
          </div>
          
          <!-- 标签卡片 -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
              <Tag class="w-5 h-5 text-blue-600 mr-2" />
              <h2 class="text-lg font-semibold text-gray-900">标签</h2>
            </div>
            
            <!-- 已添加的标签 -->
            <div v-if="formData.tags.length > 0" class="flex flex-wrap gap-2 mb-4">
              <span 
                v-for="(tag, index) in formData.tags" 
                :key="index"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-700"
              >
                {{ tag }}
                <button 
                  type="button"
                  @click="removeTag(index)"
                  class="ml-2 hover:text-blue-900"
                >
                  <X class="w-3 h-3" />
                </button>
              </span>
            </div>
            
            <!-- 添加标签 -->
            <div class="flex space-x-2">
              <input 
                v-model="tagInput"
                type="text" 
                placeholder="输入标签名称"
                @keydown="handleTagKeydown"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
              <button 
                type="button"
                @click="addTag"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                添加
              </button>
            </div>
          </div>
        </div>
        
        <!-- 右侧：图片上传 -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
              <Camera class="w-5 h-5 text-blue-600 mr-2" />
              <h2 class="text-lg font-semibold text-gray-900">票据图片</h2>
            </div>
            
            <!-- 图片预览 -->
            <div v-if="imagePreview" class="mb-4">
              <div class="relative">
                <img 
                  :src="imagePreview" 
                  alt="票据预览" 
                  class="w-full h-48 object-cover rounded-md border border-gray-300"
                >
                <button 
                  type="button"
                  @click="removeImage"
                  class="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <X class="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <!-- 上传区域 -->
            <div v-else class="relative border-2 border-dashed border-gray-300 rounded-md p-6 text-center hover:border-blue-400 transition-colors">
              <Upload class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-sm text-gray-600 mb-2">点击上传或拖拽图片到此处</p>
              <p class="text-xs text-gray-500">支持 JPG、PNG 格式，最大 5MB</p>
              <input 
                type="file" 
                accept="image/*"
                @change="handleImageUpload"
                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              >
            </div>
            
            <!-- 重新上传按钮 -->
            <div v-if="imagePreview" class="mt-4">
              <label class="block w-full">
                <span class="sr-only">重新上传图片</span>
                <input 
                  type="file" 
                  accept="image/*"
                  @change="handleImageUpload"
                  class="hidden"
                >
                <button 
                  type="button"
                  class="w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                  @click="(($event.target as HTMLElement).previousElementSibling as HTMLInputElement)?.click()"
                >
                  重新上传
                </button>
              </label>
            </div>
            
            <!-- OCR提示 -->
            <div class="mt-4 p-3 bg-blue-50 rounded-md">
              <p class="text-xs text-blue-700">
                💡 上传图片后将自动识别票据信息
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
        <button 
          type="button"
          @click="goBack"
          class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          取消
        </button>
        <button 
          type="submit"
          :disabled="loading"
          class="flex items-center px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Save class="w-4 h-4 mr-2" />
          {{ loading ? '保存中...' : '保存票据' }}
        </button>
      </div>
    </form>
  </div>
</template>