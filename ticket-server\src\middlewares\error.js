/**
 * 错误处理中间件
 * 用于统一处理应用程序中的错误
 */
const logger = require('../utils/logger');

/**
 * 自定义错误类
 * 用于创建带有状态码的错误
 */
class AppError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true; // 标记为可操作的错误

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 开发环境错误处理
 * 提供详细的错误信息
 */
const sendErrorDev = (err, req, res) => {
  logger.error(`${err.statusCode || 500} - ${err.message} - ${req.originalUrl} - ${req.method} - ${req.ip}`);
  logger.error(err.stack);
  
  res.status(err.statusCode || 500).json({
    status: 'error',
    error: err,
    message: err.message,
    stack: err.stack
  });
};

/**
 * 生产环境错误处理
 * 只提供必要的错误信息
 */
const sendErrorProd = (err, req, res) => {
  // 可操作的错误：发送给客户端
  if (err.isOperational) {
    logger.error(`${err.statusCode || 500} - ${err.message} - ${req.originalUrl} - ${req.method} - ${req.ip}`);
    
    res.status(err.statusCode || 500).json({
      status: 'error',
      message: err.message
    });
  } 
  // 编程错误：不泄露错误详情
  else {
    logger.error('非操作性错误: ' + err.stack);
    
    res.status(500).json({
      status: 'error',
      message: '服务器内部错误'
    });
  }
};

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  
  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, req, res);
  } else {
    sendErrorProd(err, req, res);
  }
};

/**
 * 捕获未处理的路由
 */
const notFoundHandler = (req, res, next) => {
  const err = new AppError(`找不到路径: ${req.originalUrl}`, 404);
  next(err);
};

module.exports = {
  AppError,
  errorHandler,
  notFoundHandler
};