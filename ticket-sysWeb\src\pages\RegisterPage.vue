<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100">
    <div class="max-w-md w-full space-y-8 p-8">
      <!-- Logo 和标题 -->
      <div class="text-center">
        <div class="mx-auto h-16 w-16 bg-green-600 rounded-full flex items-center justify-center">
          <UserPlus class="h-8 w-8 text-white" />
        </div>
        <h2 class="mt-6 text-3xl font-bold text-gray-900">创建新账户</h2>
        <p class="mt-2 text-sm text-gray-600">加入票据助手管理平台</p>
      </div>

      <!-- 注册表单 -->
      <form @submit.prevent="handleRegister" class="mt-8 space-y-6">
        <div class="space-y-4">
          <!-- 用户名输入框 -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-700">
              用户名 <span class="text-red-500">*</span>
            </label>
            <div class="mt-1 relative">
              <input
                id="username"
                v-model="registerForm.username"
                type="text"
                required
                :class="[
                  'appearance-none relative block w-full px-3 py-3 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm',
                  errors.username ? 'border-red-300' : ''
                ]"
                placeholder="请输入用户名"
              >
              <User class="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            </div>
            <p v-if="errors.username" class="mt-1 text-sm text-red-600">{{ errors.username }}</p>
          </div>

          <!-- 邮箱输入框 -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              邮箱地址 <span class="text-red-500">*</span>
            </label>
            <div class="mt-1 relative">
              <input
                id="email"
                v-model="registerForm.email"
                type="email"
                required
                :class="[
                  'appearance-none relative block w-full px-3 py-3 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm',
                  errors.email ? 'border-red-300' : ''
                ]"
                placeholder="请输入邮箱地址"
              >
              <Mail class="absolute left-3 top-3 h-5 w-5 text-gray-400" />
            </div>
            <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
          </div>

          <!-- 密码输入框 -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              密码 <span class="text-red-500">*</span>
            </label>
            <div class="mt-1 relative">
              <input
                id="password"
                v-model="registerForm.password"
                :type="showPassword ? 'text' : 'password'"
                required
                :class="[
                  'appearance-none relative block w-full px-3 py-3 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm',
                  errors.password ? 'border-red-300' : ''
                ]"
                placeholder="请输入密码"
              >
              <Lock class="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <button
                type="button"
                @click="showPassword = !showPassword"
                class="absolute right-3 top-3 h-5 w-5 text-gray-400 hover:text-gray-600"
              >
                <Eye v-if="!showPassword" class="h-5 w-5" />
                <EyeOff v-else class="h-5 w-5" />
              </button>
            </div>
            <p v-if="errors.password" class="mt-1 text-sm text-red-600">{{ errors.password }}</p>
            
            <!-- 密码强度指示器 -->
            <div v-if="registerForm.password" class="mt-2">
              <div class="flex space-x-1">
                <div 
                  v-for="i in 4" 
                  :key="i"
                  :class="[
                    'h-1 w-1/4 rounded-full transition-colors duration-200',
                    getPasswordStrengthColor(i)
                  ]"
                ></div>
              </div>
              <p class="mt-1 text-xs text-gray-500">{{ passwordStrengthText }}</p>
            </div>
          </div>

          <!-- 确认密码输入框 -->
          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
              确认密码 <span class="text-red-500">*</span>
            </label>
            <div class="mt-1 relative">
              <input
                id="confirmPassword"
                v-model="registerForm.confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                required
                :class="[
                  'appearance-none relative block w-full px-3 py-3 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 focus:z-10 sm:text-sm',
                  errors.confirmPassword ? 'border-red-300' : ''
                ]"
                placeholder="请再次输入密码"
              >
              <Lock class="absolute left-3 top-3 h-5 w-5 text-gray-400" />
              <button
                type="button"
                @click="showConfirmPassword = !showConfirmPassword"
                class="absolute right-3 top-3 h-5 w-5 text-gray-400 hover:text-gray-600"
              >
                <Eye v-if="!showConfirmPassword" class="h-5 w-5" />
                <EyeOff v-else class="h-5 w-5" />
              </button>
            </div>
            <p v-if="errors.confirmPassword" class="mt-1 text-sm text-red-600">{{ errors.confirmPassword }}</p>
          </div>
        </div>

        <!-- 服务条款 -->
        <div class="flex items-center">
          <input
            id="agree-terms"
            v-model="agreeTerms"
            type="checkbox"
            class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
          >
          <label for="agree-terms" class="ml-2 block text-sm text-gray-700">
            我同意
            <a href="#" class="text-green-600 hover:text-green-500">服务条款</a>
            和
            <a href="#" class="text-green-600 hover:text-green-500">隐私政策</a>
          </label>
        </div>
        <p v-if="errors.agreeTerms" class="text-sm text-red-600">{{ errors.agreeTerms }}</p>

        <!-- 注册按钮 -->
        <div>
          <button
            type="submit"
            :disabled="loading"
            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <span v-if="loading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <Loader2 class="h-5 w-5 text-white animate-spin" />
            </span>
            {{ loading ? '注册中...' : '创建账户' }}
          </button>
        </div>

        <!-- 登录链接 -->
        <div class="text-center">
          <span class="text-sm text-gray-600">已有账户？</span>
          <router-link
            to="/login"
            class="ml-1 text-sm font-medium text-green-600 hover:text-green-500"
          >
            立即登录
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { UserPlus, User, Mail, Lock, Eye, EyeOff, Loader2 } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import { useUserStore } from '@/stores'
import { authApi } from '@/api'
import type { RegisterForm } from '@/types'

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const agreeTerms = ref(false)

// 表单数据
const registerForm = reactive<RegisterForm>({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeToTerms: false
})

// 表单验证错误
const errors = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreeTerms: ''
})

/**
 * 计算密码强度
 */
const passwordStrength = computed(() => {
  const password = registerForm.password
  let strength = 0
  
  if (password.length >= 8) strength++
  if (/[a-z]/.test(password)) strength++
  if (/[A-Z]/.test(password)) strength++
  if (/[0-9]/.test(password)) strength++
  if (/[^A-Za-z0-9]/.test(password)) strength++
  
  return Math.min(strength, 4)
})

/**
 * 密码强度文本
 */
const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value
  const texts = ['很弱', '弱', '中等', '强', '很强']
  return texts[strength] || '很弱'
})

/**
 * 获取密码强度颜色
 * @param index 指示器索引
 * @returns 颜色类名
 */
const getPasswordStrengthColor = (index: number): string => {
  const strength = passwordStrength.value
  if (index <= strength) {
    if (strength <= 1) return 'bg-red-500'
    if (strength <= 2) return 'bg-yellow-500'
    if (strength <= 3) return 'bg-blue-500'
    return 'bg-green-500'
  }
  return 'bg-gray-200'
}

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @returns 是否有效
 */
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证表单数据
 * @returns 验证是否通过
 */
const validateForm = (): boolean => {
  // 重置错误信息
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
  
  let isValid = true
  
  // 验证用户名
  if (!registerForm.username.trim()) {
    errors.username = '请输入用户名'
    isValid = false
  } else if (registerForm.username.length < 3) {
    errors.username = '用户名至少3个字符'
    isValid = false
  } else if (registerForm.username.length > 20) {
    errors.username = '用户名不能超过20个字符'
    isValid = false
  } else if (!/^[a-zA-Z0-9_]+$/.test(registerForm.username)) {
    errors.username = '用户名只能包含字母、数字和下划线'
    isValid = false
  }
  
  // 验证邮箱
  if (!registerForm.email.trim()) {
    errors.email = '请输入邮箱地址'
    isValid = false
  } else if (!isValidEmail(registerForm.email)) {
    errors.email = '请输入有效的邮箱地址'
    isValid = false
  }
  
  // 验证密码
  if (!registerForm.password) {
    errors.password = '请输入密码'
    isValid = false
  } else if (registerForm.password.length < 6) {
    errors.password = '密码至少6个字符'
    isValid = false
  } else if (registerForm.password.length > 50) {
    errors.password = '密码不能超过50个字符'
    isValid = false
  }
  
  // 验证确认密码
  if (!registerForm.confirmPassword) {
    errors.confirmPassword = '请确认密码'
    isValid = false
  } else if (registerForm.password !== registerForm.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }
  
  // 验证服务条款
  if (!agreeTerms.value) {
    errors.agreeTerms = '请同意服务条款和隐私政策'
    isValid = false
  }
  
  return isValid
}

/**
 * 处理注册提交
 */
const handleRegister = async () => {
  if (!validateForm()) {
    return
  }
  
  loading.value = true
  
  try {
    // 调用注册 API
    const response = await authApi.register(registerForm)
    
    // 保存用户信息和 token
    userStore.setUser(response.user)
    userStore.setToken(response.token)
    
    toast.success('注册成功！欢迎加入票据助手管理平台')
    
    // 跳转到首页
    router.push('/')
    
  } catch (error: any) {
    console.error('注册失败:', error)
    
    // 显示错误信息
    const errorMessage = error.response?.data?.message || '注册失败，请稍后重试'
    toast.error(errorMessage)
    
    // 处理特定错误
    if (error.response?.data?.field) {
      const field = error.response.data.field
      if (field in errors) {
        errors[field as keyof typeof errors] = errorMessage
      }
    }
  } finally {
    loading.value = false
  }
}
</script>