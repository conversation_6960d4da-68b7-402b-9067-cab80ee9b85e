/**
 * 验证规则
 * 定义各种请求的验证规则
 */
const { body, param, query } = require('express-validator');

/**
 * 用户登录验证规则
 */
const loginRules = [
  body('username')
    .notEmpty().withMessage('用户名不能为空')
    .isLength({ min: 3 }).withMessage('用户名长度不能少于3个字符'),
  body('password')
    .notEmpty().withMessage('密码不能为空')
    .isLength({ min: 6 }).withMessage('密码长度不能少于6个字符')
];

/**
 * 用户注册验证规则
 */
const registerRules = [
  body('username')
    .notEmpty().withMessage('用户名不能为空')
    .isLength({ min: 3 }).withMessage('用户名长度不能少于3个字符'),
  body('password')
    .notEmpty().withMessage('密码不能为空')
    .isLength({ min: 6 }).withMessage('密码长度不能少于6个字符'),
  body('email')
    .notEmpty().withMessage('邮箱不能为空')
    .isEmail().withMessage('邮箱格式不正确')
];

/**
 * ID参数验证规则
 */
const idParamRules = [
  param('id')
    .notEmpty().withMessage('ID不能为空')
    .isInt().withMessage('ID必须是整数')
];

module.exports = {
  loginRules,
  registerRules,
  idParamRules
};