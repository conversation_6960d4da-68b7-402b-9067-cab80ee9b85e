/**
 * 用户相关路由
 */
const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../middlewares/auth');
const { idParamRules } = require('../middlewares/validationRules');
const { validate } = require('../middlewares/validator');
const { AppError } = require('../middlewares/error');
const logger = require('../utils/logger');
const { upload } = require('../middlewares/upload');
const { getAllUsers, getUserById, updateUserStatus, deleteUser } = require('../models/userModel');

// 获取用户列表（需要认证）
router.get('/', authenticateJWT, async (req, res, next) => {
  try {
    logger.info('获取用户列表');
    
    // 获取查询参数
    const {
      page = 1,
      pageSize = 20,
      search = '',
      role = null
    } = req.query;

    // 从数据库获取用户列表
    const result = await getAllUsers({
      page: parseInt(page),
      limit: parseInt(pageSize),
      search,
      status: null
    });

    // 转换数据格式以匹配前端期望
    const transformedUsers = result.users.map(user => ({
      id: user.user_id.toString(),
      username: user.username,
      email: user.email,
      role: user.role === 3 ? 'admin' : 'user',
      status: user.status === 1 ? 'active' : 'inactive',
      createdAt: user.register_time,
      lastLoginAt: user.last_login_time,
      avatar: user.avatar_url,
      nickname: user.nickname
    }));

    // 按角色过滤（如果指定了角色）
    let filteredUsers = transformedUsers;
    if (role) {
      filteredUsers = transformedUsers.filter(user => user.role === role);
    }

    const response = {
      list: filteredUsers,
      pagination: {
        current: result.pagination.page,
        pageSize: result.pagination.limit,
        total: result.pagination.total,
        totalPages: result.pagination.totalPages
      }
    };

    res.success(response, '获取用户列表成功');
  } catch (error) {
    logger.error(`获取用户列表失败: ${error.message}`);
    next(error);
  }
});

// 获取当前用户信息（需要认证）
router.get('/me', authenticateJWT, (req, res, next) => {
  try {
    logger.info(`获取用户信息: ${req.user.id}`);
    // req.user 包含了JWT令牌中的用户信息
    res.success(req.user, '获取当前用户信息成功');
  } catch (error) {
    logger.error(`获取用户信息失败: ${error.message}`);
    next(error);
  }
});

// 获取指定用户信息（需要认证）
router.get('/:id', authenticateJWT, idParamRules, validate, async (req, res, next) => {
  try {
    const userId = req.params.id;
    logger.info(`获取用户信息: ${userId}`);
    
    // 从数据库获取指定用户的信息
    const user = await getUserById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    res.success(user, '获取用户信息成功');
  } catch (error) {
    logger.error(`获取用户信息失败: ${error.message}`);
    next(error);
  }
});

// 创建用户（需要认证）
router.post('/', authenticateJWT, async (req, res, next) => {
  try {
    const { username, email, password, role } = req.body;
    
    if (!username || !email || !password || !role) {
      throw new AppError('用户名、邮箱、密码和角色不能为空', 400);
    }
    
    logger.info(`创建用户: ${username}`);
    
    // 模拟创建用户
    const newUser = {
      id: Date.now().toString(),
      username,
      email,
      role,
      status: 'active',
      createdAt: new Date().toISOString(),
      lastLoginAt: null
    };
    
    res.success(newUser, '用户创建成功');
  } catch (error) {
    logger.error(`创建用户失败: ${error.message}`);
    next(error);
  }
});

// 更新用户（需要认证）
router.put('/:id', authenticateJWT, idParamRules, validate, async (req, res, next) => {
  try {
    const userId = req.params.id;
    const updateData = req.body;
    
    logger.info(`更新用户: ${userId}`);
    
    // 模拟更新用户
    const updatedUser = {
      id: userId,
      ...updateData,
      updatedAt: new Date().toISOString()
    };
    
    res.success(updatedUser, '用户更新成功');
  } catch (error) {
    logger.error(`更新用户失败: ${error.message}`);
    next(error);
  }
});

// 删除用户（需要认证）
router.delete('/:id', authenticateJWT, idParamRules, validate, async (req, res, next) => {
  try {
    const userId = req.params.id;
    
    logger.info(`删除用户: ${userId}`);
    
    // 模拟删除用户
    res.success(null, '用户删除成功');
  } catch (error) {
    logger.error(`删除用户失败: ${error.message}`);
    next(error);
  }
});

// 上传用户头像（需要认证）
router.post('/avatar', authenticateJWT, upload.single('avatar'), (req, res, next) => {
  try {
    if (!req.file) {
      throw new AppError('请选择要上传的文件', 400);
    }
    
    logger.info(`用户 ${req.user.id} 上传了头像: ${req.file.filename}`);
    
    // 这里应该是更新用户头像的逻辑
    // 作为示例，返回文件信息
    res.success({
      filename: req.file.filename,
      path: `/uploads/${req.file.filename}`,
      size: req.file.size
    }, '头像上传成功');
  } catch (error) {
    logger.error(`上传头像失败: ${error.message}`);
    next(error);
  }
});

module.exports = router;