<template>
  <div class="w-full h-full">
    <div v-if="loading" class="flex items-center justify-center h-full">
      <Loader2 class="w-8 h-8 animate-spin text-gray-400" />
    </div>
    <div v-else-if="error" class="flex items-center justify-center h-full text-gray-500">
      <div class="text-center">
        <AlertCircle class="w-8 h-8 mx-auto mb-2" />
        <p>图表加载失败</p>
      </div>
    </div>
    <div v-else ref="chartContainer" class="w-full h-full"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Loader2, AlertCircle } from 'lucide-vue-next'
import * as echarts from 'echarts'
import { statisticsApi } from '@/api'

// 定义组件属性
interface Props {
  period: 'week' | 'month' | 'quarter'
}

const props = defineProps<Props>()

// 响应式数据
const chartContainer = ref<HTMLElement>()
const loading = ref(true)
const error = ref(false)
let chartInstance: echarts.ECharts | null = null

/**
 * 生成模拟数据
 * @param period 时间周期
 * @returns 图表数据
 */
const generateMockData = (period: string) => {
  const now = new Date()
  const data: { date: string; tickets: number; amount: number }[] = []
  
  let days = 7
  if (period === 'month') days = 30
  if (period === 'quarter') days = 90
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(now)
    date.setDate(date.getDate() - i)
    
    data.push({
      date: date.toISOString().split('T')[0],
      tickets: Math.floor(Math.random() * 20) + 5,
      amount: Math.floor(Math.random() * 50000) + 10000
    })
  }
  
  return data
}

/**
 * 初始化图表
 */
const initChart = async () => {
  if (!chartContainer.value) return
  
  loading.value = true
  error.value = false
  
  try {
    // 这里可以调用真实的 API
    // const data = await statisticsApi.getChartData(props.period)
    
    // 暂时使用模拟数据
    const data = generateMockData(props.period)
    
    // 销毁现有图表实例
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    // 创建新的图表实例
    chartInstance = echarts.init(chartContainer.value)
    
    // 配置图表选项
    const option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: (params: any) => {
          const data = params[0]
          return `
            <div class="p-2">
              <div class="font-medium">${data.axisValue}</div>
              <div class="text-blue-600">票据数量: ${data.value}张</div>
              <div class="text-green-600">金额: ¥${(data.data.amount / 10000).toFixed(1)}万</div>
            </div>
          `
        }
      },
      legend: {
        data: ['票据数量', '金额(万元)'],
        top: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: data.map(item => {
          const date = new Date(item.date)
          return props.period === 'week' 
            ? date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
            : date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
        }),
        axisLabel: {
          color: '#6b7280'
        },
        axisLine: {
          lineStyle: {
            color: '#e5e7eb'
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '票据数量',
          position: 'left',
          axisLabel: {
            color: '#6b7280',
            formatter: '{value}张'
          },
          axisLine: {
            lineStyle: {
              color: '#e5e7eb'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f3f4f6'
            }
          }
        },
        {
          type: 'value',
          name: '金额(万元)',
          position: 'right',
          axisLabel: {
            color: '#6b7280',
            formatter: '{value}万'
          },
          axisLine: {
            lineStyle: {
              color: '#e5e7eb'
            }
          }
        }
      ],
      series: [
        {
          name: '票据数量',
          type: 'line',
          smooth: true,
          data: data.map(item => ({
            value: item.tickets,
            data: item
          })),
          itemStyle: {
            color: '#3b82f6'
          },
          lineStyle: {
            color: '#3b82f6',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(59, 130, 246, 0.3)'
                },
                {
                  offset: 1,
                  color: 'rgba(59, 130, 246, 0.05)'
                }
              ]
            }
          }
        },
        {
          name: '金额(万元)',
          type: 'line',
          smooth: true,
          yAxisIndex: 1,
          data: data.map(item => ({
            value: (item.amount / 10000).toFixed(1),
            data: item
          })),
          itemStyle: {
            color: '#10b981'
          },
          lineStyle: {
            color: '#10b981',
            width: 2
          }
        }
      ]
    }
    
    // 设置图表选项
    chartInstance.setOption(option)
    
    // 监听窗口大小变化
    const handleResize = () => {
      chartInstance?.resize()
    }
    window.addEventListener('resize', handleResize)
    
  } catch (err) {
    console.error('图表初始化失败:', err)
    error.value = true
  } finally {
    loading.value = false
  }
}

/**
 * 清理图表实例
 */
const cleanup = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', () => chartInstance?.resize())
}

// 监听 period 变化
watch(() => props.period, () => {
  nextTick(() => {
    initChart()
  })
})

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  cleanup()
})
</script>