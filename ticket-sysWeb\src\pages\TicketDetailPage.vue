<template>
  <div class="p-6">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <RefreshCw class="w-8 h-8 animate-spin text-blue-600 mr-3" />
      <span class="text-lg text-gray-600">加载中...</span>
    </div>

    <!-- 票据详情 -->
    <div v-else-if="ticket" class="max-w-4xl mx-auto">
      <!-- 页面头部 -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
          <button
            @click="goBack"
            class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <ArrowLeft class="w-5 h-5" />
          </button>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">{{ ticket.title }}</h1>
            <p class="text-sm text-gray-500">票据编号: {{ ticket.ticketNumber }}</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full" :class="getStatusColor(ticket.status)">
            {{ getStatusLabel(ticket.status) }}
          </span>
          
          <div class="flex items-center space-x-2">
            <router-link
              :to="`/tickets/${ticket.id}/edit`"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Edit class="w-4 h-4 mr-2" />
              编辑
            </router-link>
            
            <button
              @click="handleDelete"
              class="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              <Trash2 class="w-4 h-4 mr-2" />
              删除
            </button>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 主要信息 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 基本信息卡片 -->
          <div class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">票据类型</label>
                <div class="flex items-center">
                  <component :is="getTypeIcon(ticket.type)" class="w-5 h-5 mr-2" :class="getTypeColor(ticket.type)" />
                  <span class="text-gray-900">{{ getTypeLabel(ticket.type) }}</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">金额</label>
                <div class="text-2xl font-bold" :class="getAmountColor(ticket.type)">
                  {{ formatAmount(ticket.amount, ticket.type) }}
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
                <div class="text-gray-900">{{ formatDate(ticket.createdAt) }}</div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">更新时间</label>
                <div class="text-gray-900">{{ formatDate(ticket.updatedAt) }}</div>
              </div>
              
              <div v-if="ticket.dueDate">
                <label class="block text-sm font-medium text-gray-700 mb-1">到期时间</label>
                <div class="text-gray-900">{{ formatDate(ticket.dueDate) }}</div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">创建人</label>
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                    <User class="w-4 h-4 text-blue-600" />
                  </div>
                  <span class="text-gray-900">{{ ticket.createdBy || '未知' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 描述信息 -->
          <div v-if="ticket.description" class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">描述信息</h2>
            <div class="prose max-w-none">
              <p class="text-gray-700 whitespace-pre-wrap">{{ ticket.description }}</p>
            </div>
          </div>

          <!-- 附件信息 -->
          <div v-if="ticket.attachments && ticket.attachments.length > 0" class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">附件</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div
                v-for="attachment in ticket.attachments"
                :key="attachment"
                class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                <Paperclip class="w-5 h-5 text-gray-400 mr-3" />
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900 truncate">{{ attachment }}</p>
                  <p class="text-sm text-gray-500">{{ formatFileSize(1024) }}</p>
                </div>
                <button
                  @click="downloadAttachment(attachment)"
                  class="ml-3 text-blue-600 hover:text-blue-800"
                >
                  <Download class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="space-y-6">
          <!-- 状态历史 -->
          <div class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">状态历史</h2>
            
            <div class="space-y-4">
              <div
                v-for="(log, index) in statusHistory"
                :key="index"
                class="flex items-start space-x-3"
              >
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="getStatusBgColor(log.status)">
                    <component :is="getStatusIcon(log.status)" class="w-4 h-4" :class="getStatusIconColor(log.status)" />
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm font-medium text-gray-900">{{ getStatusLabel(log.status) }}</p>
                  <p class="text-sm text-gray-500">{{ formatDate(log.createdAt) }}</p>
                  <p v-if="log.remark" class="text-sm text-gray-600 mt-1">{{ log.remark }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作历史 -->
          <div class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">操作历史</h2>
            
            <div class="space-y-3">
              <div
                v-for="(log, index) in operationLogs"
                :key="index"
                class="text-sm"
              >
                <div class="flex items-center justify-between">
                  <span class="text-gray-900">{{ log.action }}</span>
                  <span class="text-gray-500">{{ formatDate(log.timestamp) }}</span>
                </div>
                <div class="text-gray-600">{{ log.operatorName || '系统' }}</div>
                <div v-if="log.details" class="text-gray-500 mt-1">{{ log.details }}</div>
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="bg-white rounded-lg shadow-sm border p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h2>
            
            <div class="space-y-3">
              <button
                v-if="ticket.status === 'pending'"
                @click="updateStatus(TicketStatus.PROCESSING)"
                class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                开始处理
              </button>
              
              <button
                v-if="ticket.status === TicketStatus.PROCESSING"
                @click="updateStatus(TicketStatus.COMPLETED)"
                class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                标记完成
              </button>
              
              <button
                v-if="['pending', 'processing'].includes(ticket.status)"
                @click="updateStatus(TicketStatus.CANCELLED)"
                class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                取消票据
              </button>
              
              <button
                @click="exportTicket"
                class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                导出票据
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="flex items-center justify-center py-12">
      <div class="text-center">
        <AlertCircle class="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 class="text-xl font-semibold text-gray-900 mb-2">票据不存在</h2>
        <p class="text-gray-600 mb-4">请检查票据ID是否正确</p>
        <button
          @click="goBack"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          返回列表
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeft,
  Edit,
  Trash2,
  RefreshCw,
  User,
  Paperclip,
  Download,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  ArrowRightLeft,
  RotateCcw,
  Clock,
  Play,
  CheckCircle,
  XCircle
} from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import dayjs from 'dayjs'
import { ticketApi } from '@/api'
import type { Ticket, TicketType, OperationLog } from '@/types'
import { TicketStatus } from '@/types'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const ticket = ref<Ticket | null>(null)
const operationLogs = ref<OperationLog[]>([])

// 计算属性
const statusHistory = computed(() => {
  if (!ticket.value) return []
  
  // 模拟状态历史数据
  const history = [
    {
      status: 'pending' as TicketStatus,
      createdAt: ticket.value.createdAt,
      remark: '票据创建'
    }
  ]
  
  if (ticket.value.status !== 'pending') {
    history.push({
      status: ticket.value.status,
      createdAt: ticket.value.updatedAt,
      remark: '状态更新'
    })
  }
  
  return history.reverse()
})

/**
 * 获取票据详情
 */
const fetchTicket = async () => {
  try {
    loading.value = true
    const ticketId = route.params.id as string
    
    const response = await ticketApi.getTicket(ticketId)
    ticket.value = response
    
    // 获取操作日志
    await fetchOperationLogs(ticketId)
  } catch (error) {
    console.error('获取票据详情失败:', error)
    toast.error('获取票据详情失败')
  } finally {
    loading.value = false
  }
}

/**
 * 获取操作日志
 */
const fetchOperationLogs = async (ticketId: string) => {
  try {
    // 模拟操作日志数据
    operationLogs.value = [
      {
        id: '1',
        ticketId: ticketId,
        action: '创建票据',
        operator: '1',
        operatorName: 'admin',
        timestamp: ticket.value?.createdAt || new Date().toISOString(),
        details: '初始创建'
      }
    ]
  } catch (error) {
    console.error('获取操作日志失败:', error)
  }
}

/**
 * 更新票据状态
 */
const updateStatus = async (status: TicketStatus) => {
  if (!ticket.value) return
  
  try {
    await ticketApi.updateTicket(ticket.value.id, { status })
    ticket.value.status = status
    ticket.value.updatedAt = new Date().toISOString()
    toast.success('状态更新成功')
    
    // 重新获取数据
    await fetchTicket()
  } catch (error) {
    console.error('更新状态失败:', error)
    toast.error('更新状态失败')
  }
}

/**
 * 删除票据
 */
const handleDelete = async () => {
  if (!ticket.value) return
  
  if (!confirm(`确定要删除票据"${ticket.value.title}"吗？`)) {
    return
  }
  
  try {
    await ticketApi.deleteTicket(ticket.value.id)
    toast.success('删除成功')
    router.push('/tickets')
  } catch (error) {
    console.error('删除票据失败:', error)
    toast.error('删除失败')
  }
}

/**
 * 导出票据
 */
const exportTicket = () => {
  if (!ticket.value) return
  
  // 模拟导出功能
  const data = {
    ...ticket.value,
    exportTime: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `ticket-${ticket.value.ticketNumber}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  toast.success('导出成功')
}

/**
 * 下载附件
 */
const downloadAttachment = (attachment: any) => {
  // 模拟下载功能
  toast.success(`开始下载: ${attachment.name}`)
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 获取类型图标
 */
const getTypeIcon = (type: TicketType) => {
  const icons = {
    income: TrendingUp,
    expense: TrendingDown,
    transfer: ArrowRightLeft,
    refund: RotateCcw
  }
  return icons[type] || TrendingUp
}

/**
 * 获取类型颜色
 */
const getTypeColor = (type: TicketType) => {
  const colors = {
    income: 'text-green-600',
    expense: 'text-red-600',
    transfer: 'text-blue-600',
    refund: 'text-orange-600'
  }
  return colors[type] || 'text-gray-600'
}

/**
 * 获取类型标签
 */
const getTypeLabel = (type: TicketType) => {
  const labels = {
    income: '收入',
    expense: '支出',
    transfer: '转账',
    refund: '退款'
  }
  return labels[type] || type
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status: TicketStatus) => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800'
  }
  return colors[status] || 'bg-gray-100 text-gray-800'
}

/**
 * 获取状态背景颜色
 */
const getStatusBgColor = (status: TicketStatus) => {
  const colors = {
    pending: 'bg-yellow-100',
    processing: 'bg-blue-100',
    completed: 'bg-green-100',
    cancelled: 'bg-red-100'
  }
  return colors[status] || 'bg-gray-100'
}

/**
 * 获取状态图标
 */
const getStatusIcon = (status: TicketStatus) => {
  const icons = {
    pending: Clock,
    processing: Play,
    completed: CheckCircle,
    cancelled: XCircle
  }
  return icons[status] || Clock
}

/**
 * 获取状态图标颜色
 */
const getStatusIconColor = (status: TicketStatus) => {
  const colors = {
    pending: 'text-yellow-600',
    processing: 'text-blue-600',
    completed: 'text-green-600',
    cancelled: 'text-red-600'
  }
  return colors[status] || 'text-gray-600'
}

/**
 * 获取状态标签
 */
const getStatusLabel = (status: TicketStatus) => {
  const labels = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return labels[status] || status
}

/**
 * 获取金额颜色
 */
const getAmountColor = (type: TicketType) => {
  const colors = {
    income: 'text-green-600',
    expense: 'text-red-600',
    transfer: 'text-blue-600',
    refund: 'text-orange-600'
  }
  return colors[type] || 'text-gray-900'
}

/**
 * 格式化金额
 */
const formatAmount = (amount: number, type: TicketType) => {
  const prefix = type === 'income' ? '+' : type === 'expense' ? '-' : ''
  return `${prefix}¥${amount.toLocaleString()}`
}

/**
 * 格式化日期
 */
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化文件大小
 */
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTicket()
})
</script>