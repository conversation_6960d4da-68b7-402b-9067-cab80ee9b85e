/**
 * 应用程序入口文件
 */
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const helmet = require('helmet');
const path = require('path');
require('dotenv').config();

// 加载环境变量
const NODE_ENV = process.env.NODE_ENV || 'development';

// 导入自定义中间件
const { errorHandler, notFoundHandler } = require('./middlewares/error');
const { handleMulterErrors } = require('./middlewares/upload');
const { createRateLimiter } = require('./middlewares/rateLimit');
const { responseHandler } = require('./middlewares/response');
const logger = require('./utils/logger');

// 导入数据库配置
const { testConnection } = require('./config/db');

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const adminRoutes = require('./routes/admin');
const ticketRoutes = require('./routes/tickets');

// 导入模型
const { initAdminTable } = require('./models/adminModel');
const { initTicketTable } = require('./models/ticketModel');
const { initUserTable } = require('./models/userModel');

// 创建Express应用
const app = express();

// 配置中间件
// 配置CORS - 允许所有来源的跨域请求
app.use(cors({
  origin: '*', // 允许所有来源
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(helmet()); // 添加安全头部设置
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 统一响应格式中间件
app.use(responseHandler);

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 请求日志记录
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.originalUrl} - ${req.ip}`);
  next();
});

// 文件上传错误处理
app.use(handleMulterErrors);

// 配置路由 - 添加速率限制
app.use('/api/auth', createRateLimiter('/api/auth'), authRoutes);
app.use('/api/users', createRateLimiter('/api/users'), userRoutes);
app.use('/api/admin', createRateLimiter('/api/admin'), adminRoutes);
app.use('/api/tickets', createRateLimiter('/api/tickets'), ticketRoutes);

// 根路由
app.get('/', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: '欢迎使用Ticket Server API',
    env: NODE_ENV
  })
});

// 处理404错误
app.use(notFoundHandler);

// 全局错误处理
app.use(errorHandler);

// 获取端口
const PORT = process.env.PORT || 4000;

// 启动服务器
app.listen(PORT, async () => {
  logger.info(`服务器运行在 http://localhost:${PORT}`);
  
  // 测试数据库连接并初始化管理员表
  try {
    await testConnection();
    
    // 初始化管理员表
    await initAdminTable();
    logger.info('管理员表初始化完成');
    
    // 初始化票据表
    await initTicketTable();
    logger.info('票据表初始化完成');
    
    // 初始化用户表
    await initUserTable();
    logger.info('用户表初始化完成');
  } catch (error) {
    logger.error(`数据库操作失败: ${error.message}`);
  }
});

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
  logger.error('未捕获的异常:', err);
  process.exit(1);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
});

module.exports = app;