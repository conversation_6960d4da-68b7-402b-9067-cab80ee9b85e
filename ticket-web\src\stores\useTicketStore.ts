import { reactive, computed } from 'vue'
import type { 
  Ticket, 
  TicketQueryParams, 
  TicketStats, 
  VerificationResult 
} from '@/types'
import { TicketStatus } from '@/types'
import * as api from '@/services/api'

interface TicketStoreState {
  // 票据数据
  tickets: Ticket[]
  currentTicket: Ticket | null
  total: number
  
  // 统计数据
  stats: TicketStats | null
  
  // 状态
  loading: boolean
  error: string | null
  
  // 分页
  currentPage: number
  pageSize: number
  
  // 筛选
  filters: Partial<TicketQueryParams>
  
  // 核对结果
  verificationResults: VerificationResult[]
  
  // 选中的票据
  selectedTickets: string[]
}

const state = reactive<TicketStoreState>({
  // 初始状态
  tickets: [],
  currentTicket: null,
  total: 0,
  stats: null,
  loading: false,
  error: null,
  currentPage: 1,
  pageSize: 20,
  filters: {},
  selectedTickets: [],
  verificationResults: [],
})

export const useTicketStore = () => ({
  // 状态
  ...state,
  
  // 计算属性
  hasSelectedTickets: computed(() => state.selectedTickets.length > 0),
  isAllSelected: computed(() => 
    state.tickets.length > 0 && state.selectedTickets.length === state.tickets.length
  ),
  
  // 获取票据列表
  fetchTickets: async (params?: Partial<TicketQueryParams>) => {
    state.loading = true
    state.error = null
    
    try {
      const queryParams = {
        ...state.filters,
        ...params,
        page: params?.page || state.currentPage,
        pageSize: params?.pageSize || state.pageSize
      }
      
      const response = await api.getTickets(queryParams)
      
      if (response.code === 200 && response.data) {
        state.tickets = response.data.data.tickets
        state.total = response.data.data.total
        state.currentPage = response.data.data.page
        state.pageSize = response.data.data.pageSize
        state.loading = false
      } else {
        throw new Error(response.message || '获取票据列表失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '获取票据列表失败'
      state.loading = false
    }
  },

  // 根据ID获取票据详情
  fetchTicketById: async (id: string) => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.getTicketById(id)
      
      if (response.code === 200 && response.data) {
        state.currentTicket = response.data.data
        state.loading = false
      } else {
        throw new Error(response.message || '获取票据详情失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '获取票据详情失败'
      state.loading = false
    }
  },

  // 获取单个票据（返回票据对象）
  getTicketById: async (id: string): Promise<Ticket | null> => {
    try {
      const response = await api.getTicketById(id)
      
      if (response.code === 200 && response.data) {
        return response.data.data
      } else {
        throw new Error(response.message || '获取票据详情失败')
      }
    } catch (error) {
      console.error('获取票据详情失败:', error)
      return null
    }
  },

  // 获取票据统计
  fetchTicketStats: async () => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.getTicketStats()
      
      if (response.code === 200 && response.data) {
        state.stats = response.data.data
        state.loading = false
      } else {
        throw new Error(response.message || '获取统计数据失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '获取统计数据失败'
      state.loading = false
    }
  },

  // 创建票据
  createTicket: async (ticketData: Omit<Ticket, 'id' | 'createdAt' | 'updatedAt'>): Promise<boolean> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.createTicket(ticketData)
      
      if (response.code === 201 && response.data) {
        state.tickets = [response.data.data, ...state.tickets]
        state.total = state.total + 1
        state.loading = false
        return true
      } else {
        throw new Error(response.message || '创建票据失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '创建票据失败'
      state.loading = false
      return false
    }
  },

  // 更新票据
  updateTicket: async (id: string, updates: Partial<Ticket>): Promise<boolean> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.updateTicket(id, updates)
      
      if (response.code === 200 && response.data) {
        state.tickets = state.tickets.map(ticket => 
          ticket.id === id ? response.data.data : ticket
        )
        if (state.currentTicket?.id === id) {
          state.currentTicket = response.data.data
        }
        state.loading = false
        return true
      } else {
        throw new Error(response.message || '更新票据失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '更新票据失败'
      state.loading = false
      return false
    }
  },

  // 删除票据
  deleteTicket: async (id: string): Promise<boolean> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.deleteTicket(id)
      
      if (response.code === 200) {
        state.tickets = state.tickets.filter(ticket => ticket.id !== id)
        state.total = state.total - 1
        state.selectedTickets = state.selectedTickets.filter(selectedId => selectedId !== id)
        if (state.currentTicket?.id === id) {
          state.currentTicket = null
        }
        state.loading = false
        return true
      } else {
        throw new Error(response.message || '删除票据失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '删除票据失败'
      state.loading = false
      return false
    }
  },

  // 批量删除票据
  deleteTickets: async (ids: string[]): Promise<boolean> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.deleteTickets(ids)
      
      if (response.code === 200) {
        state.tickets = state.tickets.filter(ticket => !ids.includes(ticket.id))
        state.total = state.total - (response.data.data || 0)
        state.selectedTickets = []
        state.loading = false
        return true
      } else {
        throw new Error(response.message || '批量删除失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '批量删除失败'
      state.loading = false
      return false
    }
  },

  // 核对票据
  verifyTicket: async (id: string, isValid: boolean, issues?: string[]): Promise<boolean> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.verifyTicket(id)
      
      if (response.code === 200 && response.data) {
        const result = response.data.data
        
        // 更新票据状态
        state.tickets = state.tickets.map(ticket => 
          ticket.id === id 
            ? { ...ticket, status: result.isValid ? TicketStatus.VERIFIED : TicketStatus.REJECTED, updatedAt: new Date().toISOString() }
            : ticket
        )
        
        if (state.currentTicket?.id === id) {
          state.currentTicket = { 
            ...state.currentTicket, 
            status: result.isValid ? TicketStatus.VERIFIED : TicketStatus.REJECTED, 
            updatedAt: new Date().toISOString() 
          }
        }
        
        state.verificationResults = [...state.verificationResults, result]
        state.loading = false
        return result.isValid
      } else {
        throw new Error(response.message || '票据核对失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '票据核对失败'
      state.loading = false
      return false
    }
  },

  // 批量核对票据
  verifyTickets: async (ids: string[]): Promise<boolean> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.verifyTickets(ids)
      
      if (response.code === 200 && response.data) {
        const results = response.data.data
        
        // 更新票据状态
        state.tickets = state.tickets.map(ticket => {
          const result = results.find((r: VerificationResult) => r.ticketId === ticket.id)
          return result 
            ? { ...ticket, status: result.isValid ? TicketStatus.VERIFIED : TicketStatus.REJECTED, updatedAt: new Date().toISOString() }
            : ticket
        })
        
        state.verificationResults = [...state.verificationResults, ...results]
        state.selectedTickets = []
        state.loading = false
        return true
      } else {
        throw new Error(response.message || '批量核对失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '批量核对失败'
      state.loading = false
      return false
    }
  },

  // 获取统计数据
  getTicketStats: async (): Promise<TicketStats | null> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.getTicketStats()
      
      if (response.code === 200 && response.data) {
        state.stats = response.data.data
        state.loading = false
        return response.data.data
      } else {
        throw new Error(response.message || '获取统计数据失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '获取统计数据失败'
      state.loading = false
      return null
    }
  },

  // 导出票据
  exportTickets: async (format: 'excel' | 'pdf', ids?: string[]): Promise<boolean> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.exportTickets(format, ids)
      
      if (response.code === 200 && response.data) {
        state.loading = false
        // 触发下载
        const link = document.createElement('a')
        link.href = response.data.data.url
        link.download = response.data.data.filename
        link.click()
        return true
      } else {
        throw new Error(response.message || '导出失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '导出失败'
      state.loading = false
      return false
    }
  },
  
  // 上传并识别票据
  uploadAndRecognize: async (file: File): Promise<any> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.uploadAndRecognizeTicket(file)
      
      if (response.code === 200 && response.data) {
        state.loading = false
        return response.data.data
      } else {
        throw new Error(response.message || 'OCR识别失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : 'OCR识别失败'
      state.loading = false
      return null
    }
  },
  
  // 设置筛选条件
  setFilters: (filters: Partial<TicketQueryParams>) => {
    state.filters = { ...state.filters, ...filters }
    state.currentPage = 1 // 重置到第一页
  },
  
  // 清除筛选条件
  clearFilters: () => {
    state.filters = {}
    state.currentPage = 1
  },
  
  // 设置选中的票据
  setSelectedTickets: (ids: string[]) => {
    state.selectedTickets = ids
  },
  
  // 切换票据选中状态
  toggleTicketSelection: (id: string) => {
    if (state.selectedTickets.includes(id)) {
      state.selectedTickets = state.selectedTickets.filter(selectedId => selectedId !== id)
    } else {
      state.selectedTickets = [...state.selectedTickets, id]
    }
  },
  
  // 全选票据
  selectAllTickets: () => {
    state.selectedTickets = state.tickets.map(ticket => ticket.id)
  },
  
  // 清除选择
  clearSelection: () => {
    state.selectedTickets = []
  },
  
  // 设置当前页
  setCurrentPage: (page: number) => {
    state.currentPage = page
  },
  
  // 设置页面大小
  setPageSize: (size: number) => {
    state.pageSize = size
    state.currentPage = 1 // 重置到第一页
  },
  
  // 清除错误
  clearError: () => {
    state.error = null
  },
  
  // 获取验证统计数据
  getVerificationStats: async (): Promise<any> => {
    state.loading = true
    state.error = null
    
    try {
      const response = await api.getTicketStats()
      
      if (response.code === 200 && response.data) {
        state.loading = false
        return response.data.data
      } else {
        throw new Error(response.message || '获取验证统计数据失败')
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : '获取验证统计数据失败'
      state.loading = false
      return null
    }
  }
})