// 用户角色枚举
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user'
}

// 票据状态枚举
export enum TicketStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  PROCESSING = 'processing',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 票据类型枚举
export enum TicketType {
  INVOICE = 'invoice',
  RECEIPT = 'receipt',
  INCOME = 'income',
  EXPENSE = 'expense',
  OTHER = 'other'
}

// 用户接口定义
export interface User {
  id: string
  username: string
  email: string
  role: UserRole
  avatar?: string
  status: 'active' | 'inactive'
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
}

// 管理员接口定义
export interface Admin {
  admin_id: number
  username: string
  real_name: string
  email: string
  mobile: string
  avatar: string
  role_type: string
  status: number
  last_login_time: string
}

// 登录响应接口
export interface LoginResponse {
  status: string
  message: string
  data: {
    token: string
    admin: Admin
  }
}

// 票据接口定义
export interface Ticket {
  id: string
  ticketNumber: string
  title: string
  type: TicketType
  amount: number
  description?: string
  status: TicketStatus
  attachments?: string[]
  createdBy: string
  createdAt: string
  updatedAt: string
  approvedBy?: string
  approvedAt?: string
  dueDate?: string
}

// 操作历史记录接口
export interface OperationLog {
  id: string
  ticketId: string
  action: string
  operator: string
  operatorName: string
  timestamp: string
  details?: string
}

// API 响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页接口
export interface Pagination {
  current: number
  pageSize: number
  total: number
}

// 分页查询参数
export interface PageQuery {
  current?: number
  page?: number
  pageSize?: number
  keyword?: string
  search?: string
  status?: string
  type?: string
  role?: string
  startDate?: string
  endDate?: string
}

// 统计数据接口
export interface Statistics {
  totalTickets: number
  monthlyNew: number
  pendingCount: number
  approvedCount: number
  totalAmount: number
  monthlyAmount: number
}

// 表单接口
export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

export interface UserCreateForm {
  username: string
  email: string
  password: string
  role: UserRole
}

export interface UserUpdateForm {
  username?: string
  email?: string
  role?: UserRole
  status?: 'active' | 'inactive'
}

export interface TicketForm {
  title: string
  type: TicketType
  amount: number
  description?: string
  status: TicketStatus
  dueDate?: string
  ticketNumber?: string
}