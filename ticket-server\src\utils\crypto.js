/**
 * 加密工具函数
 * 用于密码加密和验证
 */
const crypto = require('crypto');
const logger = require('./logger');

/**
 * 生成随机盐值
 * @param {Number} length - 盐值长度，默认为16
 * @returns {String} 生成的盐值
 */
const generateSalt = (length = 16) => {
  try {
    return crypto.randomBytes(Math.ceil(length / 2))
      .toString('hex')
      .slice(0, length);
  } catch (error) {
    logger.error(`生成盐值失败: ${error.message}`);
    throw new Error('生成盐值失败');
  }
};

/**
 * 使用盐值加密密码
 * @param {String} password - 原始密码
 * @param {String} salt - 盐值
 * @returns {String} 加密后的密码
 */
const hashPassword = (password, salt) => {
  try {
    const hash = crypto.createHmac('sha512', salt);
    hash.update(password);
    return hash.digest('hex');
  } catch (error) {
    logger.error(`密码加密失败: ${error.message}`);
    throw new Error('密码加密失败');
  }
};

/**
 * 验证密码是否匹配
 * @param {String} password - 要验证的密码
 * @param {String} hash - 存储的密码哈希
 * @param {String} salt - 存储的盐值
 * @returns {Boolean} 密码是否匹配
 */
const verifyPassword = (password, hash, salt) => {
  try {
    const passwordHash = hashPassword(password, salt);
    return passwordHash === hash;
  } catch (error) {
    logger.error(`密码验证失败: ${error.message}`);
    return false;
  }
};

module.exports = {
  generateSalt,
  hashPassword,
  verifyPassword
};