<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/useAuthStore'
import { 
  Menu, 
  X, 
  Home, 
  Plus, 
  FileText, 
  Search, 
  CheckCircle, 
  BarChart3, 
  Settings,
  User,
  LogOut,
  ChevronDown,
  Users
} from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 侧边栏展开状态
const sidebarOpen = ref(true)

// 用户菜单展开状态
const userMenuOpen = ref(false)

// 导航菜单项
const menuItems = computed(() => {
  const baseItems = [
    { name: '首页', path: '/', icon: Home },
    { name: '票据录入', path: '/create', icon: Plus },
    { name: '票据列表', path: '/tickets', icon: FileText },
    { name: '票据核对', path: '/verify', icon: CheckCircle },
    { name: '统计分析', path: '/analytics', icon: BarChart3 }
  ]
  
  // 企业用户显示用户管理菜单
  if (authStore.isEnterprise) {
    baseItems.push({ name: '用户管理', path: '/users', icon: Users })
  }
  
  baseItems.push({ name: '设置', path: '/settings', icon: Settings })
  
  return baseItems
})

/**
 * 切换侧边栏展开状态
 */
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

/**
 * 导航到指定路径
 * @param path 路径
 */
const navigateTo = (path: string) => {
  router.push(path)
}

/**
 * 检查当前路径是否激活
 * @param path 路径
 */
const isActive = (path: string) => {
  return route.path === path
}

// 当前页面标题
const pageTitle = computed(() => {
  const currentItem = menuItems.value.find(item => item.path === route.path)
  return currentItem?.name || '票据助手'
})

/**
 * 切换用户菜单
 */
const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
}

/**
 * 处理登出
 */
const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <div 
      :class="[
        'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      ]"
    >
      <!-- 侧边栏头部 -->
      <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200">
        <h1 class="text-xl font-bold text-blue-600">票据助手</h1>
        <button 
          @click="toggleSidebar"
          class="p-2 rounded-md hover:bg-gray-100 lg:hidden"
        >
          <X class="w-5 h-5" />
        </button>
      </div>
      
      <!-- 导航菜单 -->
      <nav class="mt-6">
        <div class="px-3">
          <div 
            v-for="item in menuItems" 
            :key="item.path"
            @click="navigateTo(item.path)"
            :class="[
              'flex items-center px-3 py-2 mb-1 text-sm font-medium rounded-md cursor-pointer transition-colors duration-200',
              isActive(item.path) 
                ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-600' 
                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
            ]"
          >
            <component :is="item.icon" class="w-5 h-5 mr-3" />
            {{ item.name }}
          </div>
        </div>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div :class="['transition-all duration-300', sidebarOpen ? 'lg:ml-64' : 'ml-0']">
      <!-- 顶部导航栏 -->
      <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between h-16 px-6">
          <div class="flex items-center">
            <!-- 移动端菜单按钮 -->
            <button 
              @click="toggleSidebar"
              class="p-2 rounded-md hover:bg-gray-100 lg:hidden mr-3"
            >
              <Menu class="w-5 h-5" />
            </button>
            
            <!-- 桌面端菜单按钮 -->
            <button 
              @click="toggleSidebar"
              class="hidden lg:block p-2 rounded-md hover:bg-gray-100 mr-3"
            >
              <Menu class="w-5 h-5" />
            </button>
            
            <!-- 页面标题 -->
            <h2 class="text-lg font-semibold text-gray-900">{{ pageTitle }}</h2>
          </div>
          
          <!-- 右侧操作区 -->
          <div class="flex items-center space-x-4">
            <!-- 搜索框 -->
            <div class="relative hidden md:block">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search class="h-4 w-4 text-gray-400" />
              </div>
              <input 
                type="text" 
                placeholder="搜索票据..."
                class="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
            </div>
            
            <!-- 用户菜单 -->
            <div class="relative">
              <button 
                @click="toggleUserMenu"
                class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <!-- 用户头像 -->
                <div class="w-8 h-8 rounded-full overflow-hidden bg-blue-500 flex items-center justify-center">
                  <img 
                    v-if="authStore.user?.avatar" 
                    :src="authStore.user.avatar" 
                    :alt="authStore.user.name"
                    class="w-full h-full object-cover"
                  />
                  <User v-else class="w-4 h-4 text-white" />
                </div>
                
                <!-- 用户名 -->
                <div class="hidden md:block text-left">
                  <p class="text-sm font-medium text-gray-900">{{ authStore.user?.name || '用户' }}</p>
                  <p class="text-xs text-gray-500">{{ authStore.user?.role === 'enterprise' ? '企业用户' : '普通用户' }}</p>
                </div>
                
                <ChevronDown class="w-4 h-4 text-gray-400" />
              </button>
              
              <!-- 用户下拉菜单 -->
              <div 
                v-if="userMenuOpen"
                class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
              >
                <div class="px-4 py-3 border-b border-gray-100">
                  <p class="text-sm font-medium text-gray-900">{{ authStore.user?.name }}</p>
                  <p class="text-xs text-gray-500">{{ authStore.user?.email }}</p>
                </div>
                
                <button
                  @click="handleLogout"
                  class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <LogOut class="w-4 h-4 mr-3" />
                  登出
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <main class="p-6">
        <slot />
      </main>
    </div>
    
    <!-- 移动端遮罩层 -->
    <div 
      v-if="sidebarOpen" 
      @click="toggleSidebar"
      class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
    ></div>
  </div>
</template>

<style scoped>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>