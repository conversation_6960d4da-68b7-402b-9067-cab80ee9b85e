/**
 * JWT工具函数
 * 用于生成和验证JWT令牌
 */
const jwt = require('jsonwebtoken');
require('dotenv').config();
const logger = require('./logger');
const { AppError } = require('../middlewares/error');

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1d';

/**
 * 生成JWT令牌
 * @param {Object} payload - 要编码到令牌中的数据
 * @returns {String} JWT令牌
 * @throws {Error} 如果生成令牌失败
 */
const generateToken = (payload) => {
  try {
    logger.debug(`为用户 ${payload.id || 'unknown'} 生成令牌`);
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
  } catch (error) {
    logger.error(`生成令牌失败: ${error.message}`);
    throw new AppError('生成认证令牌失败', 500);
  }
};

/**
 * 验证JWT令牌
 * @param {String} token - 要验证的JWT令牌
 * @returns {Object|null} 解码后的令牌数据或null（如果验证失败）
 */
const verifyToken = (token) => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    logger.debug(`验证令牌成功: 用户 ${decoded.id || 'unknown'}`);
    return decoded;
  } catch (error) {
    logger.warn(`JWT验证失败: ${error.message}`);
    return null;
  }
};

/**
 * 刷新JWT令牌
 * @param {String} token - 要刷新的JWT令牌
 * @returns {String} 新的JWT令牌
 * @throws {Error} 如果刷新令牌失败
 */
const refreshToken = (token) => {
  try {
    // 验证当前令牌
    const decoded = jwt.verify(token, JWT_SECRET, { ignoreExpiration: true });
    
    // 删除令牌中的过期时间和发行时间
    delete decoded.exp;
    delete decoded.iat;
    
    // 生成新令牌
    logger.debug(`刷新用户 ${decoded.id || 'unknown'} 的令牌`);
    return jwt.sign(decoded, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
  } catch (error) {
    logger.error(`刷新令牌失败: ${error.message}`);
    throw new AppError('刷新认证令牌失败', 401);
  }
};

module.exports = {
  generateToken,
  verifyToken,
  refreshToken
};