# 票据助手

#### 介绍
该票据助手项目是在2025年7月31日晚间的直播间里用Trae新建的项目。
该票务系统包含两个客户端（前端）网站，一个是官网客户端网站ticket-web项目，还有一个是后台管理系统网站ticket-sysWeb项目。
该票务系统的后台服务端系统ticket-server提供RESTful API接口，供以上前端网站调用。

该文档是我在初始化该项目到gitee时，让Trae帮我生成的，因为所有网站代码都是用Trae生成的，因此这个简介说明文档咱们也让Trae生成。具体提示词如下：
- 分析3个子项目，一个是官网客户端网站ticket-web项目，还有一个是后台管理系统网站ticket-sysWeb项目，后台服务端系统ticket-server提供RESTful API接口。然后把项目分析后的介绍修改到READEME.md文档中

#### 项目架构

##### 整体架构

本项目采用前后端分离的架构，由三个子项目组成：

1. **ticket-web**：面向用户的官网客户端，提供票据上传、查询、验证等功能
2. **ticket-sysWeb**：面向管理员的后台管理系统，提供票据管理、用户管理、统计分析等功能
3. **ticket-server**：后端服务，为前两个前端项目提供RESTful API接口

##### 技术栈

###### ticket-web（官网客户端）
- **前端框架**：Vue 3 + TypeScript
- **构建工具**：Vite
- **UI组件**：自定义组件 + Tailwind CSS
- **状态管理**：Vue 响应式API
- **路由**：Vue Router
- **HTTP请求**：Axios
- **图表**：ECharts + Vue-ECharts

###### ticket-sysWeb（后台管理系统）
- **前端框架**：Vue 3 + TypeScript
- **构建工具**：Vite
- **UI组件**：自定义组件 + Tailwind CSS
- **状态管理**：Pinia
- **路由**：Vue Router
- **HTTP请求**：Axios
- **图表**：ECharts + Vue-ECharts
- **日期处理**：Day.js

###### ticket-server（后端服务）
- **运行环境**：Node.js
- **Web框架**：Express
- **数据库**：MySQL
- **ORM**：原生SQL（使用mysql2/promise）
- **认证**：JWT（jsonwebtoken）
- **文件上传**：multer
- **安全**：helmet、cors、express-rate-limit
- **日志**：winston
- **环境变量**：dotenv

##### 主要功能

###### ticket-web（官网客户端）
- 票据上传与识别
- 票据列表查询与筛选
- 票据详情查看
- 票据验证
- 票据导出
- 票据统计数据展示

###### ticket-sysWeb（后台管理系统）
- 管理员登录与认证
- 票据管理（查询、审批、删除等）
- 用户管理
- 统计分析（总体统计、月度统计、年度统计等）
- 系统设置

###### ticket-server（后端服务）
- 用户认证与授权
- 票据CRUD操作
- 文件上传处理
- 数据统计分析
- 错误处理与日志记录

##### 数据模型

项目主要涉及以下数据模型：

1. **票据（Ticket）**：包含类型、标题、金额、日期、描述、状态等字段
2. **用户（User）**：包含用户名、邮箱、角色、状态等字段
3. **管理员（Admin）**：包含用户名、真实姓名、邮箱、手机、角色类型等字段
4. **操作日志（OperationLog）**：记录对票据的操作历史

##### 前后端交互

前端通过Axios发起HTTP请求，后端提供RESTful API接口。主要交互流程：

1. 用户在前端操作（如登录、查询票据等）
2. 前端通过API调用后端服务
3. 后端处理请求，返回响应数据
4. 前端接收响应，更新UI展示

**注意**：API响应数据可能存在双层嵌套的data结构（response.data.data.xxx），需要在前端处理时特别注意。

#### 安装教程

1.  克隆项目到本地
2.  分别进入三个子项目目录，运行`npm install`安装依赖
3.  配置环境变量（参考各子项目中的.env.example文件）

#### 使用说明

1.  启动后端服务：在ticket-server目录下运行`npm run dev`
2.  启动官网客户端：在ticket-web目录下运行`npm run dev`
3.  启动后台管理系统：在ticket-sysWeb目录下运行`npm run dev`

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request



#### 交流方式

1. 抖音：
![alt text](抖音.jpg)

2. 微信：
![alt text](微信.jpg)